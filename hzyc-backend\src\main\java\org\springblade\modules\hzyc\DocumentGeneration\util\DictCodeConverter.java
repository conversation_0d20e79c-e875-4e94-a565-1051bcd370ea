package org.springblade.modules.hzyc.DocumentGeneration.util;

import cn.hutool.json.JSONNull;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 字典代码转换工具类
 * 用于将各种代码转换为对应的中文名称
 *
 * <AUTHOR>
 */
public class DictCodeConverter {

    /**
     * 安全地将对象转换为字符串，处理JSONNull情况
     */
    public static String safeToString(Object obj) {
        if (obj == null || obj instanceof JSONNull) {
            return null;
        }
        return obj.toString();
    }

    /**
     * 性别代码转换
     * @param sexCode 性别代码
     * @return 性别中文名称
     */
    public static String convertSexCode(String sexCode) {
        if (sexCode == null) {
            return null;
        }
        
        switch (sexCode) {
            case "01":
                return "男";
            case "02":
                return "女";
            default:
                return sexCode;
        }
    }

    /**
     * 民族代码转换
     * @param nationCode 民族代码
     * @return 民族中文名称
     */
    public static String convertNationCode(String nationCode) {
        if (nationCode == null) {
            return null;
        }
        
        switch (nationCode) {
            case "01":
                return "汉族";
            case "02":
                return "蒙古族";
            case "03":
                return "回族";
            case "04":
                return "藏族";
            case "05":
                return "维吾尔族";
            case "06":
                return "苗族";
            case "07":
                return "彝族";
            case "08":
                return "壮族";
            case "09":
                return "布依族";
            case "10":
                return "朝鲜族";
            case "11":
                return "满族";
            case "12":
                return "侗族";
            case "13":
                return "瑶族";
            case "14":
                return "白族";
            case "15":
                return "土家族";
            case "16":
                return "哈尼族";
            case "17":
                return "哈萨克族";
            case "18":
                return "傣族";
            case "19":
                return "黎族";
            case "20":
                return "傈僳族";
            case "21":
                return "佤族";
            case "22":
                return "畲族";
            case "23":
                return "高山族";
            case "24":
                return "拉祜族";
            case "25":
                return "水族";
            case "26":
                return "东乡族";
            case "27":
                return "纳西族";
            case "28":
                return "景颇族";
            case "29":
                return "柯尔克孜族";
            case "30":
                return "土族";
            case "31":
                return "达斡尔族";
            case "32":
                return "仫佬族";
            case "33":
                return "羌族";
            case "34":
                return "布朗族";
            case "35":
                return "撒拉族";
            case "36":
                return "毛南族";
            case "37":
                return "仡佬族";
            case "38":
                return "锡伯族";
            case "39":
                return "阿昌族";
            case "40":
                return "普米族";
            case "41":
                return "塔吉克族";
            case "42":
                return "怒族";
            case "43":
                return "乌孜别克族";
            case "44":
                return "俄罗斯族";
            case "45":
                return "鄂温克族";
            case "46":
                return "德昂族";
            case "47":
                return "保安族";
            case "48":
                return "裕固族";
            case "49":
                return "京族";
            case "50":
                return "塔塔尔族";
            case "51":
                return "独龙族";
            case "52":
                return "鄂伦春族";
            case "53":
                return "赫哲族";
            case "54":
                return "门巴族";
            case "55":
                return "珞巴族";
            case "56":
                return "基诺族";
            case "97":
                return "其他";
            case "98":
                return "外国血统中国籍人士";
            default:
                return nationCode;
        }
    }

    /**
     * 证件类型代码转换
     * @param idTypeCode 证件类型代码
     * @return 证件类型中文名称
     */
    public static String convertIdTypeCode(String idTypeCode) {
        if (idTypeCode == null) {
            return null;
        }
        
        switch (idTypeCode) {
            case "01":
                return "身份证";
            case "02":
                return "护照";
            case "03":
                return "军官证";
            case "04":
                return "警官证";
            case "05":
                return "士兵证";
            case "06":
                return "港澳居民来往内地通行证";
            case "07":
                return "台湾居民来往大陆通行证";
            case "08":
                return "外国人居留证";
            case "09":
                return "其他";
            case "10":
                return "户口簿";
            case "11":
                return "临时身份证";
            case "12":
                return "外国人永久居留身份证";
            case "13":
                return "港澳居民居住证";
            case "14":
                return "台湾居民居住证";
            default:
                return idTypeCode;
        }
    }

    /**
     * 是否标识转换
     * @param flag 标识值 (0/1)
     * @return 是否中文
     */
    public static String convertYesNoFlag(Object flag) {
        if (flag == null) {
            return null;
        }
        
        String flagStr = safeToString(flag);
        if ("1".equals(flagStr)) {
            return "是";
        } else if ("0".equals(flagStr)) {
            return "否";
        } else {
            return flagStr;
        }
    }

    /**
     * 有效无效标识转换
     * @param flag 标识值 (0/1)
     * @return 有效无效中文
     */
    public static String convertActiveFlag(Object flag) {
        if (flag == null) {
            return null;
        }
        
        String flagStr = safeToString(flag);
        if ("1".equals(flagStr)) {
            return "有效";
        } else if ("0".equals(flagStr)) {
            return "无效";
        } else {
            return flagStr;
        }
    }

    /**
     * 已读未读标识转换
     * @param flag 标识值 (0/1)
     * @return 已读未读中文
     */
    public static String convertReadFlag(Object flag) {
        if (flag == null) {
            return null;
        }

        String flagStr = safeToString(flag);
        if ("1".equals(flagStr)) {
            return "已阅读";
        } else if ("0".equals(flagStr)) {
            return "未阅读";
        } else {
            return flagStr;
        }
    }

    /**
     * 案件来源代码转换
     * @param caseFromCode 案件来源代码
     * @return 案件来源中文名称
     */
    public static String convertCaseFromCode(String caseFromCode) {
        if (caseFromCode == null) {
            return null;
        }

        switch (caseFromCode) {
            case "01":
                return "主动检查";
            case "02":
                return "群众举报";
            case "03":
                return "上级交办";
            case "04":
                return "其他部门移送";
            case "05":
                return "协查案件";
            case "06":
                return "媒体曝光";
            case "07":
                return "网络监测";
            case "08":
                return "投诉举报";
            case "09":
                return "其他";
            default:
                return caseFromCode;
        }
    }

    /**
     * 安全地转换对象为字符串，null值返回空字符串而不是"null"
     * @param obj 要转换的对象
     * @return 转换后的字符串，null时返回空字符串
     */
    public static String safeToStringWithEmpty(Object obj) {
        if (obj == null || obj instanceof JSONNull) {
            return "";
        }
        return obj.toString();
    }

    /**
     * 安全地转换对象为字符串，null值返回指定的默认值
     * @param obj 要转换的对象
     * @param defaultValue null时的默认值
     * @return 转换后的字符串
     */
    public static String safeToStringWithDefault(Object obj, String defaultValue) {
        if (obj == null || obj instanceof JSONNull) {
            return defaultValue;
        }
        return obj.toString();
    }

    /**
     * 格式化时间戳为日期字符串
     * @param timestamp 时间戳（毫秒）
     * @param pattern 日期格式模式，默认为 "yyyy年MM月dd日"
     * @return 格式化后的日期字符串
     */
    public static String formatTimestamp(Object timestamp, String pattern) {
        if (timestamp == null || timestamp instanceof JSONNull) {
            return "";
        }

        try {
            long timestampLong;
            if (timestamp instanceof String) {
                timestampLong = Long.parseLong((String) timestamp);
            } else if (timestamp instanceof Number) {
                timestampLong = ((Number) timestamp).longValue();
            } else {
                return timestamp.toString();
            }

            Date date = new Date(timestampLong);
            SimpleDateFormat sdf = new SimpleDateFormat(pattern != null ? pattern : "yyyy年MM月dd日");
            return sdf.format(date);
        } catch (Exception e) {
            // 如果转换失败，返回原始值
            return timestamp.toString();
        }
    }

    /**
     * 格式化时间戳为日期字符串（使用默认格式）
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期字符串，格式为 "yyyy年MM月dd日"
     */
    public static String formatTimestamp(Object timestamp) {
        return formatTimestamp(timestamp, "yyyy年MM月dd日");
    }

    /**
     * 格式化时间戳为标准日期格式
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期字符串，格式为 "yyyy-MM-dd"
     */
    public static String formatTimestampToStandardDate(Object timestamp) {
        return formatTimestamp(timestamp, "yyyy-MM-dd");
    }

    /**
     * 格式化时间戳为完整日期时间格式
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的日期时间字符串，格式为 "yyyy年MM月dd日 HH:mm:ss"
     */
    public static String formatTimestampToDateTime(Object timestamp) {
        return formatTimestamp(timestamp, "yyyy年MM月dd日 HH:mm:ss");
    }
}
