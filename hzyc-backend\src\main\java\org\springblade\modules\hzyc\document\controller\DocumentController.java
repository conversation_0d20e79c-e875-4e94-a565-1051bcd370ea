/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hzyc.document.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.modules.hzyc.document.pojo.vo.DocumentVO;
import org.springblade.modules.hzyc.document.excel.DocumentExcel;
import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.service.IDocumentValidationService;
import org.springblade.modules.hzyc.document.wrapper.DocumentWrapper;
import org.springblade.modules.hzyc.validation.service.IValidationResultService;
import org.springblade.modules.hzyc.document.service.IPdfSplitService;
import org.springblade.modules.hzyc.document.service.IRecognitionRecordService;
import org.springblade.modules.hzyc.document.entity.RecognitionRecord;
import org.springblade.modules.hzyc.document.pojo.vo.ValidationResultVO;
import org.springblade.modules.hzyc.document.pojo.vo.PdfSplitResultVO;
import org.springframework.web.multipart.MultipartFile;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import java.util.Map;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.ArrayList;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Base64;
import com.fasterxml.jackson.databind.ObjectMapper;
/**
 * 文书表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-document/document")
@Tag(name = "文书表", description = "文书表接口")
public class DocumentController extends BladeController {

	private final IDocumentService documentService;
	private final IDocumentValidationService documentValidationService;
	private final IValidationResultService validationResultService;
	private final IPdfSplitService pdfSplitService;
	private final IRecognitionRecordService recognitionRecordService;

	/**
	 * 文书表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入document")
	public R<DocumentVO> detail(DocumentEntity document) {
		DocumentEntity detail = documentService.getOne(Condition.getQueryWrapper(document));
		return R.data(DocumentWrapper.build().entityVO(detail));
	}
	/**
	 * 文书表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入document")
	public R<IPage<DocumentVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> document, Query query) {
		IPage<DocumentEntity> pages = documentService.page(Condition.getPage(query), Condition.getQueryWrapper(document, DocumentEntity.class));
		return R.data(DocumentWrapper.build().pageVO(pages));
	}

	/**
	 * 文书表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入document")
	public R<IPage<DocumentVO>> page(DocumentVO document, Query query) {
		IPage<DocumentVO> pages = documentService.selectDocumentPage(Condition.getPage(query), document);
		return R.data(pages);
	}

	/**
	 * 文书表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入document")
	public R save(@Valid @RequestBody DocumentEntity document) {
		return R.status(documentService.save(document));
	}

	/**
	 * 文书表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入document")
	public R update(@Valid @RequestBody DocumentEntity document) {
		return R.status(documentService.updateById(document));
	}

	/**
	 * 文书表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入document")
	public R submit(@Valid @RequestBody DocumentEntity document) {
		  try {
            // 获取Base64编码的documentContent
            String encodedContent = document.getDocumentContent();

            if (encodedContent != null && !encodedContent.isEmpty()) {
                // Base64解码
                byte[] decodedBytes = Base64.getDecoder().decode(encodedContent);
                String decodedJsonString = new String(decodedBytes, StandardCharsets.UTF_8);

                // 将解码后的JSON字符串存储到数据库
                document.setDocumentContent(decodedJsonString);
            }

            // 保存到数据库
            documentService.saveOrUpdate(document);

            return R.success("保存成功");

        } catch (IllegalArgumentException e) {
            // Base64解码失败
            return R.fail("数据格式错误：Base64解码失败");
        } catch (Exception e) {
            // JSON解析失败或其他错误
            return R.fail("保存失败：" + e.getMessage());
        }
	}

	/**
	 * 文书表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(documentService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@GetMapping("/export-document")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入document")
	public void exportDocument(@Parameter(hidden = true) @RequestParam Map<String, Object> document, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<DocumentEntity> queryWrapper = Condition.getQueryWrapper(document, DocumentEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Document::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(DocumentEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<DocumentExcel> list = documentService.exportDocument(queryWrapper);
		ExcelUtil.export(response, "文书表数据" + DateUtil.time(), "文书表数据表", list, DocumentExcel.class);
	}

	/**
	 * 文书内容校验
	 */
	@PostMapping("/validate")
	@ApiOperationSupport(order = 10)
	@Operation(summary = "文书内容校验", description = "基于一般程序案卷评查标准校验文书内容")
	public R<ValidationResultVO> validateDocument(@Valid @RequestBody DocumentEntity document) {
		try {
			// 执行文书校验
			ValidationResultVO result = documentValidationService.validateDocument(document);

			// 持久化校验结果到数据库
			validationResultService.saveValidationResult(result, document.getId());

			return R.data(result);
		} catch (Exception e) {
			return R.fail("文书校验失败: " + e.getMessage());
		}
	}

	/**
	 * 根据文书ID校验文书内容
	 */
	@PostMapping("/validate/{id}")
	@ApiOperationSupport(order = 11)
	@Operation(summary = "根据ID校验文书内容", description = "根据文书ID校验文书内容")
	public R<ValidationResultVO> validateDocumentById(@Parameter(description = "文书ID", required = true) @PathVariable Long id) {
		try {
			// 根据ID获取文书
			DocumentEntity document = documentService.getById(id);
			if (document == null) {
				return R.fail("文书不存在");
			}

			// 执行文书校验
			ValidationResultVO result = documentValidationService.validateDocument(document);

			// 持久化校验结果到数据库
			validationResultService.saveValidationResult(result, document.getId());

			return R.data(result);
		} catch (Exception e) {
			return R.fail("文书校验失败: " + e.getMessage());
		}
	}

	/**
	 * 批量校验文书
	 */
	@PostMapping("/validate/batch")
	@ApiOperationSupport(order = 12)
	@Operation(summary = "批量校验文书", description = "批量校验多个文书内容")
	public R<List<ValidationResultVO>> validateDocumentsBatch(
			@Parameter(description = "文书类型列表", required = true) @RequestBody List<String> documentTypes,
			@Parameter(description = "校验类型：case-案件级校验，doc-文书级校验", required = true) @RequestParam String type,
			@Parameter(description = "案件ID", required = true) @RequestParam Long caseId) {
		try {
			// 验证类型参数
			if (!"case".equals(type) && !"doc".equals(type)) {
				return R.fail("校验类型参数错误，只支持case或doc");
			}

			List<ValidationResultVO> results = new ArrayList<>();

			for (String documentType : documentTypes) {
				ValidationResultVO result = null;
				
				if ("doc".equals(type)) {
					// doc类型：从recognition_record表查询文件记录
					List<RecognitionRecord> records = recognitionRecordService.list(new QueryWrapper<RecognitionRecord>()
						.eq("document_type", documentType)
						.eq("case_id", caseId));
					for (RecognitionRecord record : records) {
						if (record != null && record.getExtractedContent() != null) {
							// 使用extracted_content字段进行校验
							DocumentEntity tempDoc = new DocumentEntity();
							tempDoc.setDocumentContent(record.getExtractedContent());
							tempDoc.setDocumentType(documentType);
							tempDoc.setId(caseId);
							result = documentValidationService.validateDocument(tempDoc);
							
							// 持久化校验结果到数据库
							validationResultService.saveValidationResult(result, record.getId());
							
							results.add(result);
						}
					}
				} else if ("case".equals(type)) {
					// case类型：从ca_document表查询文件信息
					List<DocumentEntity> documents = documentService.list(new QueryWrapper<DocumentEntity>()
						.eq("document_type", documentType)
						.eq("case_id", caseId));
					for (DocumentEntity document : documents) {
						if (document != null && document.getDocumentContent() != null) {
							// 使用document_content字段进行校验
							result = documentValidationService.validateDocument(document);
							
							// 持久化校验结果到数据库
							validationResultService.saveValidationResult(result, document.getId());
							
							results.add(result);
						}
					}
				}
			}

			return R.data(results);
		} catch (Exception e) {
			return R.fail("批量校验失败: " + e.getMessage());
		}
	}

	/**
	 * 获取评查结果示例数据
	 */
	@GetMapping("/validate/sample/{caseId}")
	@ApiOperationSupport(order = 13)
	@Operation(summary = "获取评查结果示例数据", description = "用于前端开发和测试的评查结果示例数据")
	public R<ValidationResultVO> getValidationSampleData(@Parameter(description = "案件ID", required = true) @PathVariable String caseId) {
		try {
			// 创建一个示例的评查结果对象用于测试
			ValidationResultVO result = new ValidationResultVO();
			result.setCaseId(caseId); // 使用setCaseId方法,因为ValidationResultVO中定义的是caseId字段
			result.setValidationTime(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATETIME));
			result.setStatus("VALID");
			return R.data(result);
		} catch (Exception e) {
			return R.fail("获取示例数据失败: " + e.getMessage());
		}
	}

	/**
	 * PDF文件拆分
	 */
	@PostMapping("/split-pdf")
	@ApiOperationSupport(order = 14)
	@Operation(summary = "PDF文件拆分", description = "将包含多个案卷文书的PDF文件拆分成独立文书")
	public R<PdfSplitResultVO> splitPdf(
			@Parameter(description = "PDF文件", required = true) @RequestParam("file") MultipartFile file,
			@Parameter(description = "案件UUID") @RequestParam(value = "caseUuid", required = false) String caseUuid,
			@Parameter(description = "案件ID") @RequestParam(value = "caseId", required = false) Long caseId) {
		try {
			PdfSplitResultVO result = pdfSplitService.splitPdf(file, caseUuid, caseId);
			return R.data(result);
		} catch (Exception e) {
			return R.fail("PDF拆分失败: " + e.getMessage());
		}
	}

	/**
	 * 异步PDF文件拆分
	 */
	@PostMapping("/split-pdf-async")
	@ApiOperationSupport(order = 15)
	@Operation(summary = "异步PDF文件拆分", description = "异步拆分PDF文件，返回任务ID")
	public R<String> splitPdfAsync(
			@Parameter(description = "PDF文件", required = true) @RequestParam("file") MultipartFile file,
			@Parameter(description = "案件UUID") @RequestParam(value = "caseUuid", required = false) String caseUuid,
			@Parameter(description = "案件ID") @RequestParam(value = "caseId", required = false) Long caseId) {
		try {
			String taskId = pdfSplitService.splitPdfAsync(file, caseUuid, caseId);
			return R.data(taskId);
		} catch (Exception e) {
			return R.fail("异步PDF拆分失败: " + e.getMessage());
		}
	}

	/**
	 * 查询PDF拆分结果
	 */
	@GetMapping("/split-result/{taskId}")
	@ApiOperationSupport(order = 16)
	@Operation(summary = "查询PDF拆分结果", description = "根据任务ID查询PDF拆分结果")
	public R<PdfSplitResultVO> getSplitResult(
			@Parameter(description = "任务ID", required = true) @PathVariable String taskId) {
		try {
			PdfSplitResultVO result = pdfSplitService.getSplitResult(taskId);
			if (result == null) {
				return R.fail("未找到对应的拆分任务");
			}
			return R.data(result);
		} catch (Exception e) {
			return R.fail("查询拆分结果失败: " + e.getMessage());
		}
	}

	/**
	 * 删除PDF拆分结果
	 */
	@DeleteMapping("/split-result/{taskId}")
	@ApiOperationSupport(order = 17)
	@Operation(summary = "删除PDF拆分结果", description = "删除指定任务的拆分结果和文件")
	public R<Boolean> deleteSplitResult(
			@Parameter(description = "任务ID", required = true) @PathVariable String taskId) {
		try {
			boolean success = pdfSplitService.deleteSplitResult(taskId);
			return R.data(success);
		} catch (Exception e) {
			return R.fail("删除拆分结果失败: " + e.getMessage());
		}
	}

	/**
	 * 验证PDF文件
	 */
	@PostMapping("/validate-pdf")
	@ApiOperationSupport(order = 18)
	@Operation(summary = "验证PDF文件", description = "验证上传的PDF文件是否符合拆分要求")
	public R<String> validatePdf(
			@Parameter(description = "PDF文件", required = true) @RequestParam("file") MultipartFile file) {
		try {
			String result = pdfSplitService.validatePdfFile(file);
			if ("VALID".equals(result)) {
				return R.success("PDF文件验证通过");
			} else {
				return R.fail(result);
			}
		} catch (Exception e) {
			return R.fail("PDF文件验证失败: " + e.getMessage());
		}
	}

	/**
	 * 根据任务ID获取拆分文件列表
	 */
	@GetMapping("/split-files/{taskId}")
	@ApiOperationSupport(order = 19)
	@Operation(summary = "根据任务ID获取拆分文件列表", description = "在PDF模式下，通过拆分任务ID查询recognition_record表获取拆分文件列表")
	public R<List<RecognitionRecord>> getSplitFilesByTaskId(
			@Parameter(description = "拆分任务ID", required = true) @PathVariable String taskId) {
		try {
			// 通过taskId查询recognition_record表中的拆分文件记录
			List<RecognitionRecord> records = recognitionRecordService.getByTaskId(taskId);

			if (records == null || records.isEmpty()) {
				return R.fail("未找到对应任务的拆分文件");
			}

			return R.data(records);
		} catch (Exception e) {
			return R.fail("获取拆分文件列表失败: " + e.getMessage());
		}
	}

}
