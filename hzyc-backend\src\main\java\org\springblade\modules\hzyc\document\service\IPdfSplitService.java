package org.springblade.modules.hzyc.document.service;

import org.springblade.modules.hzyc.document.pojo.vo.PdfSplitResultVO;
import org.springframework.web.multipart.MultipartFile;
import org.springblade.modules.hzyc.cases.pojo.entity.CasesEntity;
/**
 * PDF拆分服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IPdfSplitService {

    /**
     * 拆分PDF文件
     * 将包含多个案卷文书的PDF文件按规则拆分成独立的文书文件
     *
     * @param file 上传的PDF文件
     * @param caseUuid 案件UUID（可选）
     * @param caseId 案件ID（可选，如果传入则使用该值，否则默认为1）
     * @return 拆分结果
     */
    PdfSplitResultVO splitPdf(MultipartFile file, String caseUuid, Long caseId);

    /**
     * 根据任务ID查询拆分结果
     *
     * @param taskId 拆分任务ID
     * @return 拆分结果
     */
    PdfSplitResultVO getSplitResult(String taskId);

    /**
     * 异步拆分PDF文件
     * 适用于大文件处理，返回任务ID供后续查询
     *
     * @param file 上传的PDF文件
     * @param caseUuid 案件UUID（可选）
     * @param caseId 案件ID（可选，如果传入则使用该值，否则默认为1）
     * @return 任务ID
     */
    String splitPdfAsync(MultipartFile file, String caseUuid, Long caseId);

    /**
     * 删除拆分结果文件
     *
     * @param taskId 拆分任务ID
     * @return 是否删除成功
     */
    boolean deleteSplitResult(String taskId);

    /**
     * 验证PDF文件是否符合拆分要求
     *
     * @param file 上传的PDF文件
     * @return 验证结果信息
     */
    String validatePdfFile(MultipartFile file);

    /**
     * 从文档内容中提取文书号
     * 支持多种括号格式：﹝﹞、[]、【】、（）、()
     *
     * @param content 文档内容
     * @return 提取到的文书号，如果未找到则返回null
     */
    String extractDocumentNumber(String content);

    /**
     * 根据文书号查询案件记录
     *
     * @param documentNumber 文书号
     * @return 匹配的案件记录，如果未找到则返回null
     */
    CasesEntity findCaseByDocumentNumber(String documentNumber);

    /**
     * 处理立案报告表类型文档
     * 提取文书号并查询关联的案件记录
     *
     * @param content 文档内容
     * @return 关联的案件记录，如果未找到则返回null
     */
   CasesEntity processCaseFilingReport(String content);
}