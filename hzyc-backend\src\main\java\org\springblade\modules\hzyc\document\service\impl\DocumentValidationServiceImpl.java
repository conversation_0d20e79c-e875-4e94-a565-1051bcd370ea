/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.hzyc.document.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springblade.modules.hzyc.document.service.IDocumentValidationService;
import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.service.IRecognitionRecordService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.modules.hzyc.document.entity.RecognitionRecord;
import org.springblade.modules.hzyc.document.pojo.vo.ValidationResultVO;
import org.springblade.modules.hzyc.document.pojo.vo.ValidationItemVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文书内容校验服务实现类
 * 基于《文书内容校验（一般程序案卷评查标准）》
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
@AllArgsConstructor
public class DocumentValidationServiceImpl implements IDocumentValidationService {

    private final ObjectMapper objectMapper;
    private final IRecognitionRecordService recognitionRecordService;
    private final IDocumentService documentService;

    @Override
    public ValidationResultVO validateDocument(DocumentEntity document) {
        ValidationResultVO result = new ValidationResultVO();
        List<ValidationItemVO> validationItems = new ArrayList<>();
        
        try {
            // 解析文书内容JSON
            JsonNode documentContent = objectMapper.readTree(document.getDocumentContent());
            
            // 执行各项校验
            validationItems.addAll(validateLiAn(documentContent, document.getId())); // 立案校验（8分）
            validationItems.addAll(validateDiaoChaQuZheng(documentContent)); // 调查取证校验（39分）
            validationItems.addAll(validateShenChaJueDing(documentContent)); // 审查和决定校验（25分）
            validationItems.addAll(validateSongDaZhiXing(documentContent)); // 送达执行校验（16分）
            validationItems.addAll(validateAnJuanGuanLi(documentContent)); // 案卷管理校验（12分）
            
            // 计算总分
            double totalScore = 90.0;
            double deductedScore = validationItems.stream()
                .mapToDouble(ValidationItemVO::getDeductedScore)
                .sum();
            double finalScore = Math.max(0, totalScore - deductedScore);
            
            result.setTotalScore(totalScore);
            result.setDeductedScore(deductedScore);
            result.setFinalScore(finalScore);
            result.setValidationItems(validationItems);
            result.setValidationStatus(finalScore >= 80 ? "PASS" : "FAIL");
            
        } catch (Exception e) {
            log.error("文书校验失败", e);
            result.setValidationStatus("ERROR");
            result.setErrorMessage("文书内容解析失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取评查结果示例数据（用于前端开发和测试）
     * @param caseId 案件ID
     * @return 评查结果示例数据
     */
    public ValidationResultVO getValidationSampleData(String caseId) {
        ValidationResultVO result = new ValidationResultVO();
        List<ValidationItemVO> validationItems = new ArrayList<>();
        
        // 立案校验示例数据
        validationItems.add(createValidationItem("立案", "没有记载案件来源或案件来源与其他文书不一致", 0.5, "上下文一致性对比Agent"));
        validationItems.add(createValidationItem("立案", "没有记载当事人的基本情况，或错误记载当事人姓名、有效证件号码和地址", 1.0, "程序校验"));
        
        // 调查取证校验示例数据
        validationItems.add(createValidationItem("调查取证", "被询问人基本情况填写不全的，或没有准确记载询问时间或地点", 0.5, "程序校验"));
        validationItems.add(createValidationItem("调查取证", "笔录中对主要事实询问或记录不全面、不准确", 3.0, "逻辑矛盾检查Agent"));
        validationItems.add(createValidationItem("调查取证", "执法人员没有签名或只有一人签名", 1.0, "签名识别Agent"));
        
        // 审查和决定校验示例数据
        validationItems.add(createValidationItem("审查和决定", "没有记载或错误记载当事人基本情况", 1.0, "程序校验"));
        validationItems.add(createValidationItem("审查和决定", "没有记载违法事实或违法事实表述不清楚、不准确", 2.0, "程序校验"));
        
        // 送达执行校验示例数据
        validationItems.add(createValidationItem("送达执行", "送达回证填写不规范或缺失", 1.5, "程序校验"));
        
        // 案卷管理校验示例数据
        validationItems.add(createValidationItem("案卷管理", "案卷目录与实际文书不符", 1.0, "程序校验"));
        
        // 计算总分
        double totalScore = 90.0;
        double deductedScore = validationItems.stream()
            .mapToDouble(ValidationItemVO::getDeductedScore)
            .sum();
        double finalScore = Math.max(0, totalScore - deductedScore);
        
        result.setTotalScore(totalScore);
        result.setDeductedScore(deductedScore);
        result.setFinalScore(finalScore);
        result.setValidationItems(validationItems);
        result.setValidationStatus(finalScore >= 80 ? "PASS" : "FAIL");
        result.setValidationTime("2024-01-20 14:30:00");
        result.setValidator("系统自动评查");
        result.setRemark("案件ID: " + caseId + ", 本次评查发现 " + validationItems.size() + " 个问题，建议及时整改。");
        
        return result;
    }

    /**
     * 立案校验（8分）
     */
    private List<ValidationItemVO> validateLiAn(JsonNode documentContent, Long caseId) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 须立案而没有立案文书的（2分）- 检查是否存在立案报告表
        boolean hasLiAnDocument = checkLiAnDocumentExists(caseId);
        if (hasLiAnDocument) {
            items.add(createPassedValidationItem("立案", "须立案而没有立案文书", "程序校验"));
        } else {
            items.add(createValidationItem("立案", "须立案而没有立案文书", 2.0, "程序校验"));
        }
        
        // 2. 没有记载案件来源或案件来源与其他文书不一致的（0.5分）- 默认通过
        items.add(createPassedValidationItem("立案", "没有记载案件来源或案件来源与其他文书不一致", "上下文一致性对比Agent"));
        
        // 3. 没有记载或错误记载案由、发案时间和发案地点的（1分）- 默认通过
        items.add(createPassedValidationItem("立案", "没有记载或错误记载案由、发案时间和发案地点", "程序校验"));
        
        // 4. 没有记载当事人的基本情况，或错误记载当事人姓名、有效证件号码和地址的（1分）- 默认通过
        items.add(createPassedValidationItem("立案", "没有记载当事人的基本情况，或错误记载当事人姓名、有效证件号码和地址", "程序校验"));
        
        // 5. 没有记载案件情况或案件情况表述不清晰的（1分）- 默认通过
        items.add(createPassedValidationItem("立案", "没有记载案件情况或案件情况表述不清晰", "程序校验"));
        
        // 6. 没有立案依据或立案依据引用不准确的（0.5分）- 默认通过
        items.add(createPassedValidationItem("立案", "没有立案依据或立案依据引用不准确", "案由与法律法规关联性Agent"));
        
        // 7. 没有记载承办人、承办部门意见、签字和日期的（0.5分）- 默认通过
        items.add(createPassedValidationItem("立案", "没有记载承办人、承办部门意见、签字和日期", "程序校验"));
        
        // 8. 没有记载行政机关负责人明确意见、签字和日期的（0.5分）- 默认通过
        items.add(createPassedValidationItem("立案", "没有记载行政机关负责人明确意见、签字和日期", "程序校验"));
        
        // 9. 超过批准立案时间立案的（1分）- 默认通过
        items.add(createPassedValidationItem("立案", "超过批准立案时间立案", "程序校验"));
        
        return items;
    }

    /**
     * 调查取证校验（39分）
     */
    private List<ValidationItemVO> validateDiaoChaQuZheng(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // A. 检查（勘验）笔录（6分）
        items.addAll(validateJianChaBiLu(documentContent));
        
        // B. 询问笔录（9分）
        items.addAll(validateXunWenBiLu(documentContent));
        
        // C. 提取、固定物证及其他证据（9分）
        items.addAll(validateTiQuWuZheng(documentContent));
        
        // D. 先行登记保存（8分）
        items.addAll(validateXianXingDengJi(documentContent));
        
        // E. 调查终结（5分）
        items.addAll(validateDiaoChaZhongJie(documentContent));
        
        // F. 证据瑕疵（2分）
        items.addAll(validateZhengJuXiaCi(documentContent));
        
        return items;
    }

    /**
     * 检查（勘验）笔录校验（6分）
     */
    private List<ValidationItemVO> validateJianChaBiLu(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 被检查人基本情况记载错误或记载不全的（1分）- 默认通过
        items.add(createPassedValidationItem("检查（勘验）笔录", "被检查人基本情况记载错误或记载不全", "程序校验"));
        
        // 2. 没有记载检查（勘验）时间或者地点的（1分）- 默认通过
        items.add(createPassedValidationItem("检查（勘验）笔录", "没有记载检查（勘验）时间或者地点", "程序校验"));
        
        // 3. 检查（勘验）的内容记载不全面、不规范的，未全面描述检查过程和现场状况，或者笔录注明的烟草专卖品品种、数量与其他文书不一致的（2分）- 默认通过
        items.add(createPassedValidationItem("检查（勘验）笔录", "检查（勘验）的内容记载不全面、不规范的，未全面描述检查过程和现场状况，或者笔录注明的烟草专卖品品种、数量与其他文书不一致", "上下文一致性对比Agent"));
        
        // 4. 被检查人拒绝签署意见及姓名，执法人员没有说明情况的（1分）- 默认通过
        items.add(createPassedValidationItem("检查（勘验）笔录", "被检查人拒绝签署意见及姓名，执法人员没有说明情况", "签名识别Agent"));
        
        // 5. 当事人、见证人、执法人员签字确认不符合规定的（1分）- 默认通过
        items.add(createPassedValidationItem("检查（勘验）笔录", "当事人、见证人、执法人员签字确认不符合规定", "签名识别Agent"));
        
        return items;
    }

    /**
     * 询问笔录校验（9分）
     */
    private List<ValidationItemVO> validateXunWenBiLu(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 一份笔录同时询问两名以上被询问人的（2分）- 默认通过
        items.add(createPassedValidationItem("询问笔录", "一份笔录同时询问两名以上被询问人", "程序校验"));
        
        // 2. 被询问人基本情况填写不全的，或没有准确记载询问时间或地点的（0.5分）- 默认通过
        items.add(createPassedValidationItem("询问笔录", "被询问人基本情况填写不全的，或没有准确记载询问时间或地点", "程序校验"));
        
        // 3. 未在询问开始时表明执法人员身份，并且告知当事人享有陈述申辩权和申请回避的权利的（0.5分）- 默认通过
        items.add(createPassedValidationItem("询问笔录", "未在询问开始时表明执法人员身份，并且告知当事人享有陈述申辩权和申请回避的权利", "程序校验"));
        
        // 4. 笔录中对主要事实（包括姓名、地点、时间、行为、数量等）询问或记录不全面、不准确，或涂改处未经被询问人确认的（3分）- 默认通过
        items.add(createPassedValidationItem("询问笔录", "笔录中对主要事实（包括姓名、地点、时间、行为、数量等）询问或记录不全面、不准确，或涂改处未经被询问人确认", "逻辑矛盾检查Agent"));
        
        // 5. 被询问人没有签署"记录属实"等意思表示，或被询问人没有逐页签名的（1分）- 默认通过
        items.add(createPassedValidationItem("询问笔录", "被询问人没有签署\"记录属实\"等意思表示，或被询问人没有逐页签名", "签名识别Agent"));
        
        // 6. 被询问人拒绝签署姓名，执法人员没有说明情况的（1分）- 默认通过
        items.add(createPassedValidationItem("询问笔录", "被询问人拒绝签署姓名，执法人员没有说明情况", "签名识别Agent"));
        
        // 7. 执法人员没有签名或只有一人签名的（1分）- 默认通过
        items.add(createPassedValidationItem("询问笔录", "执法人员没有签名或只有一人签名", "签名识别Agent"));
        
        return items;
    }

    /**
     * 提取、固定物证及其他证据校验（9分）
     */
    private List<ValidationItemVO> validateTiQuWuZheng(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 抽样提取物证时，没有抽样取证物品清单的（1分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "抽样提取物证时，没有抽样取证物品清单", "程序校验"));
        
        // 2. 没有记载或没有准确记载查获物品情况、抽样取证物品数量、当事人或见证人姓名的（1分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "没有记载或没有准确记载查获物品情况、抽样取证物品数量、当事人或见证人姓名", "程序校验"));
        
        // 3. 没有填写两名承办人意见及签名、没有填写领导意见及签名的（0.5分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "没有填写两名承办人意见及签名、没有填写领导意见及签名", "签名识别Agent"));
        
        // 4. 当事人没有签名或盖章，执法人员没有说明情况的（0.5分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "当事人没有签名或盖章，执法人员没有说明情况", "盖章识别Agent"));
        
        // 5. 价格证明不符合要求的；没有涉案物品核价依据或价格来源的（1.5分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "价格证明不符合要求的；没有涉案物品核价依据或价格来源", "程序校验"));
        
        // 6. 核价文书或记录中，没有准确记载涉案物品情况的，核价错误或没有加盖印章或错误加盖的（1分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "核价文书或记录中，没有准确记载涉案物品情况的，核价错误或没有加盖印章或错误加盖", "盖章识别Agent"));
        
        // 7. 没有提取当事人身份证明或提取不规范的（1分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "没有提取当事人身份证明或提取不规范", "程序校验"));
        
        // 8. 提取视听资料类证据复制件时，没有注明制作方法、制作时间、制作人等情况的，或视听资料没有附相关话语的文字记录的（1分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "提取视听资料类证据复制件时，没有注明制作方法、制作时间、制作人等情况的，或视听资料没有附相关话语的文字记录", "程序校验"));
        
        // 9. 没有证据复制（提取）单的（1分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "没有证据复制（提取）单", "程序校验"));
        
        // 10. 没有填写或没有正确填写证据提取说明事项的（提取时间、提取地点等）、没有提取人签字或盖章的（0.5分）- 默认通过
        items.add(createPassedValidationItem("提取、固定物证及其他证据", "没有填写或没有正确填写证据提取说明事项的（提取时间、提取地点等）、没有提取人签字或盖章", "签名识别Agent"));
        
        return items;
    }

    /**
     * 先行登记保存校验（8分）
     */
    private List<ValidationItemVO> validateXianXingDengJi(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 没有先行登记保存批准书或通知书的（2分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "没有先行登记保存批准书或通知书", "程序校验"));
        
        // 2. 未在保存期限内做出处理决定的（1分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "未在保存期限内做出处理决定", "程序校验"));
        
        // 3. 先行登记保存的证据处理不符合法定方式和要求的（1分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "先行登记保存的证据处理不符合法定方式和要求", "程序校验"));
        
        // 4. 行政机关负责人没有签署意见或姓名的（0.5分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "行政机关负责人没有签署意见或姓名", "签名识别Agent"));
        
        // 5. 没有注明保存理由、内容的（保存内容包括：品种规格、数量）（1分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "没有注明保存理由、内容的（保存内容包括：品种规格、数量）", "程序校验"));
        
        // 6. 没有记载批准保存时间的（0.5分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "没有记载批准保存时间", "程序校验"));
        
        // 7. 批准书文书记载内容与通知书不一致的（0.5分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "批准书文书记载内容与通知书不一致", "上下文一致性对比Agent"));
        
        // 8. 没有行政机关印章及日期、或没有案件承办人签字或盖章的（0.5分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "没有行政机关印章及日期、或没有案件承办人签字或盖章", "盖章识别Agent"));
        
        // 9. 没有注明先行登记保存证据期限或没有注明对先行登记保存证据作出处理决定的期限的（0.5分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "没有注明先行登记保存证据期限或没有注明对先行登记保存证据作出处理决定的期限", "程序校验"));
        
        // 10. 先行登记保存物品处理通知书当事人未签字的（0.5分）- 默认通过
        items.add(createPassedValidationItem("先行登记保存", "先行登记保存物品处理通知书当事人未签字", "签名识别Agent"));
        
        return items;
    }

    /**
     * 调查终结校验（5分）
     */
    private List<ValidationItemVO> validateDiaoChaZhongJie(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 没有调查终结报告的（2分）- 默认通过
        items.add(createPassedValidationItem("调查终结", "没有调查终结报告", "程序校验"));
        
        // 2. 未在法定期限内完成调查终结的（1分）- 默认通过
        items.add(createPassedValidationItem("调查终结", "未在法定期限内完成调查终结", "程序校验"));
        
        // 3. 没有记载、错误记载或者记载不全调查经过、结果和相关案件事实的（1分）- 默认通过
        items.add(createPassedValidationItem("调查终结", "没有记载、错误记载或者记载不全调查经过、结果和相关案件事实", "程序校验"));
        
        // 4. 没有记载或者错误记载案由、立案时间、当事人基本情况的（0.5分）- 默认通过
        items.add(createPassedValidationItem("调查终结", "没有记载或者错误记载案由、立案时间、当事人基本情况", "程序校验"));
        
        // 5. 没有承办人及承办部门负责人签字、没有签署日期的（0.5分）- 默认通过
        items.add(createPassedValidationItem("调查终结", "没有承办人及承办部门负责人签字、没有签署日期", "签名识别Agent"));
        
        return items;
    }

    /**
     * 证据瑕疵校验（2分）
     */
    private List<ValidationItemVO> validateZhengJuXiaCi(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 证据存在瑕疵或造成证据自身无效，或者证据之间存在矛盾，但不影响违法事实认定或案件定性的（2分）- 默认通过
        items.add(createPassedValidationItem("证据瑕疵", "证据存在瑕疵或造成证据自身无效，或者证据之间存在矛盾，但不影响违法事实认定或案件定性", "程序校验"));
        
        // 2. 证据收集程序不规范 - 默认通过
        items.add(createPassedValidationItem("证据瑕疵", "证据收集程序不规范", "程序校验"));
        
        // 3. 证据保全措施不当 - 默认通过
        items.add(createPassedValidationItem("证据瑕疵", "证据保全措施不当", "程序校验"));
        
        return items;
    }

    /**
     * 审查和决定校验（25分）
     */
    private List<ValidationItemVO> validateShenChaJueDing(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // A. 案件处理审批（6分）
        items.addAll(validateAnJianChuLiShenPi(documentContent));
        
        // B. 行政处罚事先告知（4分）
        items.addAll(validateShiXianGaoZhi(documentContent));
        
        // C. 集体讨论（3分）
        items.addAll(validateJiTiTaoLun(documentContent));
        
        // D. 行政处罚决定（12分）
        items.addAll(validateXingZhengChuFaJueDing(documentContent));
        
        // E. 无主物品处理决定（针对无主案件，16分）
        items.addAll(validateWuZhuWuPinChuLi(documentContent));
        
        // F. 移送处理（针对移送司法机关案件，16分）
        items.addAll(validateYiSongChuLi(documentContent));
        
        return items;
    }

    /**
     * 案件处理审批校验（6分）
     */
    private List<ValidationItemVO> validateAnJianChuLiShenPi(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 当事人的基本情况或立案情况记载不准确的（0.5分）- 默认通过
        items.add(createPassedValidationItem("案件处理审批", "当事人的基本情况或立案情况记载不准确", "逻辑矛盾检查Agent"));
        
        // 2. 调查确认的违法事实、情节、后果表述不清楚的（1分）- 默认通过
        items.add(createPassedValidationItem("案件处理审批", "调查确认的违法事实、情节、后果表述不清楚", "程序校验"));
        
        // 3. 认定和处罚的依据不准确的（1分）- 默认通过
        items.add(createPassedValidationItem("案件处理审批", "认定和处罚的依据不准确", "法律条文适用性校验Agent"));
        
        // 4. 无承办人意见和签名的（0.5分）- 默认通过
        items.add(createPassedValidationItem("案件处理审批", "无承办人意见和签名", "签名识别Agent"));
        
        // 5. 无法制部门或法制员意见，或意见不明确的（2分）- 默认通过
        items.add(createPassedValidationItem("案件处理审批", "无法制部门或法制员意见，或意见不明确", "逻辑矛盾检查Agent"));
        
        // 6. 行政机关负责人审批意见不明确、或签名和审批时间填写不规范的（1分）- 默认通过
        items.add(createPassedValidationItem("案件处理审批", "行政机关负责人审批意见不明确、或签名和审批时间填写不规范", "签名识别Agent"));
        
        return items;
    }

    /**
     * 行政处罚事先告知校验（4分）
     */
    private List<ValidationItemVO> validateShiXianGaoZhi(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 行政处罚事先告知的对象错误的（1分）- 默认通过
        items.add(createPassedValidationItem("事先告知", "行政处罚事先告知的对象错误", "程序校验"));
        
        // 2. 没有全面告知事实、理由、依据和拟处罚具体内容、幅度等的（1分）- 默认通过
        items.add(createPassedValidationItem("事先告知", "没有全面告知事实、理由、依据和拟处罚具体内容、幅度等", "逻辑矛盾检查Agent"));
        
        // 3. 未告知当事人依法享有陈述、申辩的权利，或拒不听取当事人陈述、申辩的（1分）- 默认通过
        items.add(createPassedValidationItem("事先告知", "未告知当事人依法享有陈述、申辩的权利，或拒不听取当事人陈述、申辩", "程序校验"));
        
        // 4. 事先告知书没有送达当事人的（1分）- 默认通过
        items.add(createPassedValidationItem("事先告知", "事先告知书没有送达当事人", "程序校验"));
        
        return items;
    }

    /**
     * 集体讨论校验（3分）
     */
    private List<ValidationItemVO> validateJiTiTaoLun(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 按规定要进行集体讨论而没有集体讨论的（1分）- 默认通过
        items.add(createPassedValidationItem("集体讨论", "按规定要进行集体讨论而没有集体讨论", "程序校验"));
        
        // 2. 集体讨论记录没有准确记载集体讨论的时间、地点、出席人员、记录人员的姓名及其职务、案情简介及结论性意见的（1分）- 默认通过
        items.add(createPassedValidationItem("集体讨论", "集体讨论记录没有准确记载集体讨论的时间、地点、出席人员、记录人员的姓名及其职务、案情简介及结论性意见", "程序校验"));
        
        // 3. 集体讨论没有烟草专卖局负责人参加的，或参加讨论的人员对案件处理决定未提出明确意见并签字的（1分）- 默认通过
        items.add(createPassedValidationItem("集体讨论", "集体讨论没有烟草专卖局负责人参加的，或参加讨论的人员对案件处理决定未提出明确意见并签字", "签名识别Agent"));
        
        return items;
    }

    /**
     * 行政处罚决定校验（12分）
     */
    private List<ValidationItemVO> validateXingZhengChuFaJueDing(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 当事人的基本情况记载不齐全、不准确的（1分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "当事人的基本情况记载不齐全、不准确", "程序校验"));
        
        // 2. 违法事实叙述不清楚，或违法事实没有相关证据证明，逻辑性不强的（3分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "违法事实叙述不清楚，或违法事实没有相关证据证明，逻辑性不强", "逻辑矛盾检查Agent"));
        
        // 3. 处罚决定书没有列出证明案件事实的证据的（1分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "处罚决定书没有列出证明案件事实的证据", "程序校验"));
        
        // 4. 对法律、法规、规章的引用没有准确到条、款、项、目（1分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "对法律、法规、规章的引用没有准确到条、款、项、目", "法律条文适用性校验Agent"));
        
        // 5. 违法依据与处罚依据相互混同的（1分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "违法依据与处罚依据相互混同", "法律条文适用性校验Agent"));
        
        // 6. 处罚决定内容缺乏法律依据，或表述不规范的（1.5分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "处罚决定内容缺乏法律依据，或表述不规范", "逻辑矛盾检查Agent"));
        
        // 7. 行政处罚决定不符合本省自由裁量准则的（1.5分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "行政处罚决定不符合本省自由裁量准则", "裁量准则Agent"));
        
        // 8. 行政处罚的履行方式或期限告知错误的（0.5分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "行政处罚的履行方式或期限告知错误", "程序校验"));
        
        // 9. 救济途径或期限告知不明确、不正确的（0.5分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "救济途径或期限告知不明确、不正确", "程序校验"));
        
        // 10. 对调查出的部分违法事实未进行处罚的（1分）- 默认通过
        items.add(createPassedValidationItem("行政处罚决定", "对调查出的部分违法事实未进行处罚", "法律条文适用性校验Agent"));
        
        return items;
    }

    /**
     * 无主物品处理决定校验（针对无主案件，16分）
     */
    private List<ValidationItemVO> validateWuZhuWuPinChuLi(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 对有主物品按照无主案件处理的（5分）- 默认通过
        items.add(createPassedValidationItem("无主物品处理", "对有主物品按照无主案件处理", "程序校验"));
        
        // 2. 以烟草专卖局内部文件代替处理无主烟草专卖品处理决定书的（3分）- 默认通过
        items.add(createPassedValidationItem("无主物品处理", "以烟草专卖局内部文件代替处理无主烟草专卖品处理决定书", "程序校验"));
        
        // 3. 公告期未满30日作出处理决定的（3分）- 默认通过
        items.add(createPassedValidationItem("无主物品处理", "公告期未满30日作出处理决定", "程序校验"));
        
        // 4. 在作出处理决定前处理无主物品的（3分）- 默认通过
        items.add(createPassedValidationItem("无主物品处理", "在作出处理决定前处理无主物品", "程序校验"));
        
        // 5. 对无主物品作出没收等处罚的（2分）- 默认通过
        items.add(createPassedValidationItem("无主物品处理", "对无主物品作出没收等处罚", "程序校验"));
        
        return items;
    }

    /**
     * 移送处理校验（针对移送司法机关案件，16分）
     */
    private List<ValidationItemVO> validateYiSongChuLi(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 移送不符合法定时限要求的（4分）- 默认通过
        items.add(createPassedValidationItem("移送处理", "移送不符合法定时限要求的", "时限检查Agent"));
        
        // 2. 移送函填写不规范的（4分）- 默认通过
        items.add(createPassedValidationItem("移送处理", "移送函填写不规范的", "文书规范性检查Agent"));
        
        // 3. 移送材料不齐全的（4分）- 默认通过
        items.add(createPassedValidationItem("移送处理", "移送材料不齐全的", "材料完整性检查Agent"));
        
        // 4. 涉案烟草专卖品未随案移送，又无其他合法手续的（4分）- 默认通过
        items.add(createPassedValidationItem("移送处理", "涉案烟草专卖品未随案移送，又无其他合法手续的", "物品移送检查Agent"));
        
        return items;
    }

    /**
     * 送达执行校验（16分）
     */
    private List<ValidationItemVO> validateSongDaZhiXing(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // A. 文书送达（6分）
        items.addAll(validateWenShuSongDa(documentContent));
        
        // B. 处罚执行（6分）
        items.addAll(validateChuFaZhiXing(documentContent));
        
        // C. 结案（4分）
        items.addAll(validateJieAn(documentContent));
        
        return items;
    }

    /**
     * 文书送达校验（6分）
     */
    private List<ValidationItemVO> validateWenShuSongDa(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 《送达回证》内送达文书的名称、文号、受送达人的名称（姓名）、送达的时间、地点和送达方式记载不准确的（2分）- 默认通过
        items.add(createPassedValidationItem("文书送达", "《送达回证》内送达文书的名称、文号、受送达人的名称（姓名）、送达的时间、地点和送达方式记载不准确的", "送达回证检查Agent"));
        
        // 2. 其他文书对送达时间等事项记载不准确的（1分）- 默认通过
        items.add(createPassedValidationItem("文书送达", "其他文书对送达时间等事项记载不准确的", "送达时间检查Agent"));
        
        // 3. 未按法定时限送达的（2分）- 默认通过
        items.add(createPassedValidationItem("文书送达", "未按法定时限送达的", "送达时限检查Agent"));
        
        // 4. 办案单位印章、送达人签名、收件人签名及其签收时间填写不规范的（1分）- 默认通过
        items.add(createPassedValidationItem("文书送达", "办案单位印章、送达人签名、收件人签名及其签收时间填写不规范的", "签名盖章检查Agent"));
        
        return items;
    }

    /**
     * 处罚执行校验（6分）
     */
    private List<ValidationItemVO> validateChuFaZhiXing(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 对罚款、没收违法所得的处罚，未开具缴款书、没有在银行缴费的收款证明等，或填写内容与处罚决定书不一致的（1分）- 默认通过
        items.add(createPassedValidationItem("处罚执行", "对罚款、没收违法所得的处罚，未开具缴款书、没有在银行缴费的收款证明等，或填写内容与处罚决定书不一致的", "上下文一致性对比Agent"));
        
        // 2. 没收财物收据填写不规范的（0.5分）- 默认通过
        items.add(createPassedValidationItem("处罚执行", "没收财物收据填写不规范的", "程序校验"));
        
        // 3. 违法物品销毁记录对当事人姓名、案号、销毁时间、地点、数量、规格和承办人、监销人记载不清楚、不齐全的（1分）- 默认通过
        items.add(createPassedValidationItem("处罚执行", "违法物品销毁记录对当事人姓名、案号、销毁时间、地点、数量、规格和承办人、监销人记载不清楚、不齐全的", "程序校验"));
        
        // 4. 发还当事人物品与先行登记保存物品数量、规格不一致未说明原因的（1.5分）- 默认通过
        items.add(createPassedValidationItem("处罚执行", "发还当事人物品与先行登记保存物品数量、规格不一致未说明原因的", "上下文一致性对比Agent"));
        
        // 5. 未全部返还留样卷烟或鉴别检验损耗费用的（1分）- 默认通过
        items.add(createPassedValidationItem("处罚执行", "未全部返还留样卷烟或鉴别检验损耗费用的", "程序校验"));
        
        // 6. 行政处罚逾期不履行，未制作加处罚款通知书或履行行政处罚义务催告书的（1分）- 默认通过
        items.add(createPassedValidationItem("处罚执行", "行政处罚逾期不履行，未制作加处罚款通知书或履行行政处罚义务催告书的", "程序校验"));
        
        return items;
    }

    /**
     * 结案校验（4分）
     */
    private List<ValidationItemVO> validateJieAn(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 当事人名称（姓名）、违法事实和处罚内容记载不准确的（1分）- 默认通过
        items.add(createPassedValidationItem("结案", "当事人名称（姓名）、违法事实和处罚内容记载不准确的", "法律条文适用性校验Agent"));
        
        // 2. 行政处罚决定的执行结果记载不准确的（1分）- 默认通过
        items.add(createPassedValidationItem("结案", "行政处罚决定的执行结果记载不准确的", "法律条文适用性校验Agent"));
        
        // 3. 承办人、承办机构负责人和办案单位负责人的意见、签名及其时间填写不规范的（1分）- 默认通过
        items.add(createPassedValidationItem("结案", "承办人、承办机构负责人和办案单位负责人的意见、签名及其时间填写不规范的", "签名识别Agent"));
        
        // 4. 处罚执行后久拖不结案的（1分）- 默认通过
        items.add(createPassedValidationItem("结案", "处罚执行后久拖不结案的", "程序校验"));
        
        return items;
    }

    /**
     * 案卷管理校验（12分）
     */
    private List<ValidationItemVO> validateAnJuanGuanLi(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // A. 立卷归档（5分）
        items.addAll(validateLiJuanGuiDang(documentContent));
        
        // B. 卷宗制作（3分）
        items.addAll(validateJuanZongZhiZuo(documentContent));
        
        // C. 卷内文书（4分）
        items.addAll(validateJuanNeiWenShu(documentContent));
        
        return items;
    }

    /**
     * 立卷归档校验（5分）
     */
    private List<ValidationItemVO> validateLiJuanGuiDang(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 结案后未按期立卷归档的（1分）- 默认通过
        items.add(createPassedValidationItem("立卷归档", "结案后未按期立卷归档的", "程序校验"));
        
        // 2. 未实行一案一卷，一卷一号的（1分）- 默认通过
        items.add(createPassedValidationItem("立卷归档", "未实行一案一卷，一卷一号的", "程序校验"));
        
        // 3. 不能随卷保存的证据，没有放入证据袋随卷归档，或在卷内备考表注明内容、数量、责任人、存放地点的（1分）- 默认通过
        items.add(createPassedValidationItem("立卷归档", "不能随卷保存的证据，没有放入证据袋随卷归档，或在卷内备考表注明内容、数量、责任人、存放地点的", "程序校验"));
        
        // 4. 归档后又拆装案卷，擅自修改卷内材料的（2分）- 默认通过
        items.add(createPassedValidationItem("立卷归档", "归档后又拆装案卷，擅自修改卷内材料的", "程序校验"));
        
        return items;
    }

    /**
     * 卷宗制作校验（3分）
     */
    private List<ValidationItemVO> validateJuanZongZhiZuo(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 装订不整齐，纸张破损，罚没收据、举报费支付等非证据类小文书没有用衬纸粘贴，大文书没有折叠整齐，卷内出现金属物等（0.5分）- 默认通过
        items.add(createPassedValidationItem("卷宗制作", "装订不整齐，纸张破损，罚没收据、举报费支付等非证据类小文书没有用衬纸粘贴，大文书没有折叠整齐，卷内出现金属物等", "程序校验"));
        
        // 2. 封面、卷内目录填写不规范的，或与卷内材料不一致的（1分）- 默认通过
        items.add(createPassedValidationItem("卷宗制作", "封面、卷内目录填写不规范的，或与卷内材料不一致的", "程序校验"));
        
        // 3. 卷内材料页码编号不规范，出现重码、漏码、编码涂改等的（0.5分）- 默认通过
        items.add(createPassedValidationItem("卷宗制作", "卷内材料页码编号不规范，出现重码、漏码、编码涂改等的", "程序校验"));
        
        // 4. 行政处罚决定书和送达回证在前，其余文书按照办案时间顺序排列，不符合该要求的（1分）- 默认通过
        items.add(createPassedValidationItem("卷宗制作", "行政处罚决定书和送达回证在前，其余文书按照办案时间顺序排列，不符合该要求的", "程序校验"));
        
        return items;
    }

    /**
     * 卷内文书校验（4分）
     */
    private List<ValidationItemVO> validateJuanNeiWenShu(JsonNode documentContent) {
        List<ValidationItemVO> items = new ArrayList<>();
        
        // 1. 卷内文书使用复写纸等，字迹模糊的（1分）- 默认通过
        items.add(createPassedValidationItem("卷内文书", "卷内文书使用复写纸等，字迹模糊的", "程序校验"));
        
        // 2. 卷内法律文书使用铅笔、圆珠笔、红笔书写的（0.5分）- 默认通过
        items.add(createPassedValidationItem("卷内文书", "卷内法律文书使用铅笔、圆珠笔、红笔书写的", "程序校验"));
        
        // 3. 卷内文书字迹潦草，字体、大小不规范，错别字、语病多的（1.5分）- 默认通过
        items.add(createPassedValidationItem("卷内文书", "卷内文书字迹潦草，字体、大小不规范，错别字、语病多的", "文字错误识别Agent"));
        
        // 4. 卷内文书盖章不规范，修改部分未加盖校对章等方式确认，多页材料未加盖骑缝章的（1分）- 默认通过
        items.add(createPassedValidationItem("卷内文书", "卷内文书盖章不规范，修改部分未加盖校对章等方式确认，多页材料未加盖骑缝章的", "盖章识别Agent"));
        
        return items;
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建校验项
     */
    private ValidationItemVO createValidationItem(String category, String description, double deductedScore, String agentType) {
        ValidationItemVO item = new ValidationItemVO();
        item.setCategory(category);
        item.setDescription(description);
        item.setDeductedScore(deductedScore);
        item.setAgentType(agentType);
        item.setStatus("FAILED");
        return item;
    }

    private ValidationItemVO createPassedValidationItem(String category, String description, String agentType) {
        ValidationItemVO item = new ValidationItemVO();
        item.setCategory(category);
        item.setDescription(description);
        item.setDeductedScore(0.0);
        item.setAgentType(agentType);
        item.setStatus("PASSED");
        return item;
    }

    // ==================== 基础校验方法（需要根据实际文书结构实现） ====================

    private boolean hasLiAnDocument(JsonNode documentContent) {
        // TODO: 检查是否有立案文书
        return true;
    }

    private boolean hasCaseSource(JsonNode documentContent) {
        // TODO: 检查是否记载案件来源
        return true;
    }

    private boolean isCaseSourceConsistent(JsonNode documentContent) {
        // TODO: 检查案件来源是否与其他文书一致
        return true;
    }

    private boolean hasCaseInfo(JsonNode documentContent) {
        // TODO: 检查是否记载案由、发案时间和发案地点
        return true;
    }

    private boolean hasPartyInfo(JsonNode documentContent) {
        // TODO: 检查是否记载当事人基本情况
        return true;
    }

    private boolean hasCaseDescription(JsonNode documentContent) {
        // TODO: 检查是否记载案件情况
        return true;
    }

    private boolean hasLegalBasis(JsonNode documentContent) {
        // TODO: 检查是否有立案依据
        return true;
    }

    private boolean hasHandlerInfo(JsonNode documentContent) {
        // TODO: 检查是否记载承办人信息
        return true;
    }

    private boolean hasLeaderApproval(JsonNode documentContent) {
        // TODO: 检查是否有行政机关负责人意见
        return true;
    }

    private boolean isOverdueCase(JsonNode documentContent) {
        // TODO: 实现超期立案检查逻辑
        return false;
    }

    /**
     * 检查是否存在立案报告表
     * 通过案件ID查询recognition_record和ca_document表
     * 
     * @param caseId 案件ID
     * @return 如果存在立案报告表则返回true，否则返回false
     */
    private boolean checkLiAnDocumentExists(Long caseId) {
        if (caseId == null) {
            return false;
        }
        
        try {
            // 查询recognition_record表中是否存在立案报告表
            List<RecognitionRecord> recognitionRecords = recognitionRecordService.list(
                new QueryWrapper<RecognitionRecord>()
                    .eq("case_id", caseId)
                    .eq("document_type", "立案报告表")
            );
            
            if (!recognitionRecords.isEmpty()) {
                log.info("在recognition_record表中找到立案报告表，案件ID: {}", caseId);
                return true;
            }
            
            // 查询ca_document表中是否存在立案报告表
            List<DocumentEntity> documents = documentService.list(
                new QueryWrapper<DocumentEntity>()
                    .eq("case_id", caseId)
                    .eq("document_type", "立案报告表")
            );
            
            if (!documents.isEmpty()) {
                log.info("在ca_document表中找到立案报告表，案件ID: {}", caseId);
                return true;
            }
            
            log.warn("未找到立案报告表，案件ID: {}", caseId);
            return false;
            
        } catch (Exception e) {
            log.error("检查立案报告表时发生错误，案件ID: {}, 错误信息: {}", caseId, e.getMessage(), e);
            return false;
        }
    }
}