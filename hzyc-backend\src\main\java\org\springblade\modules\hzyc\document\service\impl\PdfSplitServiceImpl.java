package org.springblade.modules.hzyc.document.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springblade.modules.hzyc.document.entity.RecognitionRecord;
import org.springblade.modules.hzyc.document.pojo.vo.PdfSplitResultVO;
import org.springblade.modules.hzyc.document.pojo.vo.SplitDocumentVO;
import org.springblade.modules.hzyc.document.service.IPdfSplitService;
import org.springblade.modules.hzyc.document.service.IRecognitionRecordService;
import org.springblade.modules.hzyc.cases.service.ICasesService;
import org.springblade.modules.hzyc.cases.pojo.entity.CasesEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.core.io.ByteArrayResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import org.springblade.modules.resource.builder.OssBuilder;
import org.springblade.core.oss.model.BladeFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * PDF Split Service Implementation
 * 
 * 支持OCR引擎：
 * 1. PaddleOCR API - 主要OCR方案
 * 
 * OCR引擎选择策略：
 * - 使用PaddleOCR进行文本提取
 * - PaddleOCR失败时降级到传统PDF文本提取
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class PdfSplitServiceImpl implements IPdfSplitService {

    // Store async task results
    private final Map<String, PdfSplitResultVO> taskResults = new ConcurrentHashMap<>();
    
    // OCR结果缓存，避免对同一文件进行多次重复调用
    private final Map<String, String> ocrResultCache = new ConcurrentHashMap<>();

    @Autowired
    private IRecognitionRecordService recognitionRecordService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private OssBuilder ossBuilder;

    @Autowired
    private ICasesService casesService;

    @Value("${paddleocr.api.url:http://localhost:8000}")
    private String paddleocrApiUrl;

    @Value("${paddleocr.api.max-pages:70}")
    private int maxPagesForOcr;

    @Value("${paddleocr.use-mock-data:true}")
    private boolean useMockData;


    // TODO: Inject MinIO service
    // @Autowired
    // private MinioService minioService;

    // Document type recognition keywords - 65 standard document titles
    // 参考 generate.vue 中的 documentTypes 变量进行完善
    private static final Map<String, String> DOCUMENT_TYPE_KEYWORDS = new HashMap<>();
    
    static {
        // 基础文档类型 - 与 generate.vue 中的 documentTypes 保持一致
        DOCUMENT_TYPE_KEYWORDS.put("REPORT_RECORD", "卖局\n举报记录表");
        DOCUMENT_TYPE_KEYWORDS.put("CASE_FILING_REPORT", "卖局\n立案报告表");
        DOCUMENT_TYPE_KEYWORDS.put("NON_FILING_REPORT", "卖局\n不予立案报告表");
        DOCUMENT_TYPE_KEYWORDS.put("NON_FILING_NOTICE", "卖局\n不予立案告知书");
        DOCUMENT_TYPE_KEYWORDS.put("EXTEND_FILING_APPROVAL", "卖局\n延长立案期限审批表");
        DOCUMENT_TYPE_KEYWORDS.put("EXTEND_FILING_NOTICE", "卖局\n延长立案期限告知书");
        DOCUMENT_TYPE_KEYWORDS.put("JURISDICTION_NOTICE", "卖局\n指定管辖通知书");
        
        // 现场检查和证据收集
        DOCUMENT_TYPE_KEYWORDS.put("SCENE_RECORD", "卖局\n现场笔录");
        DOCUMENT_TYPE_KEYWORDS.put("ELECTRONIC_DATA_RECORD", "卖局\n电子数据证据提取笔录");
        DOCUMENT_TYPE_KEYWORDS.put("EVIDENCE_PRESERVATION_APPROVAL", "卖局\n证据先行登记保存批准书");
        DOCUMENT_TYPE_KEYWORDS.put("EVIDENCE_PRESERVATION_NOTICE", "卖局\n证据先行登记保存通知书");
        DOCUMENT_TYPE_KEYWORDS.put("EVIDENCE_HANDLING_NOTICE", "卖局\n先行登记保存证据处理通知书");
        DOCUMENT_TYPE_KEYWORDS.put("SAMPLING_LIST", "卖局\n抽样取证物品清单");
        DOCUMENT_TYPE_KEYWORDS.put("INSPECTION_SAMPLE_NOTICE", "卖局\n鉴别检验留样告知书");
        
        // 询问和调查
        DOCUMENT_TYPE_KEYWORDS.put("INQUIRY_NOTICE", "卖局\n询问通知书");
        DOCUMENT_TYPE_KEYWORDS.put("INQUIRY_RECORD", "卖局\n询问笔录");
        DOCUMENT_TYPE_KEYWORDS.put("EVIDENCE_COPY_FORM", "卖局\n证据复制（提取）单");
        DOCUMENT_TYPE_KEYWORDS.put("ITEM_VALUATION_FORM", "卖局\n涉案物品核价表");
        DOCUMENT_TYPE_KEYWORDS.put("ANNOUNCEMENT", "卖局\n公告");

        // 案件移送和协助
        DOCUMENT_TYPE_KEYWORDS.put("CASE_TRANSFER_LETTER", "卖局\n案件移送函");
        DOCUMENT_TYPE_KEYWORDS.put("PROPERTY_TRANSFER_LIST", "卖局\n移送财物清单");
        DOCUMENT_TYPE_KEYWORDS.put("ASSISTANCE_LETTER", "卖局\n协助函");
        DOCUMENT_TYPE_KEYWORDS.put("INVESTIGATION_REPORT", "卖局\n案件调查终结报告");
        DOCUMENT_TYPE_KEYWORDS.put("ITEM_RETURN_LIST", "卖局\n涉案物品返还清单");
        DOCUMENT_TYPE_KEYWORDS.put("SAMPLE_RETURN_LIST", "卖局\n鉴别检验留样返还清单");
        
        // 案件处理和审批
        DOCUMENT_TYPE_KEYWORDS.put("CASE_REVIEW_FORM", "卖局\n案件处理初审表");
        DOCUMENT_TYPE_KEYWORDS.put("PENALTY_NOTICE", "卖局\n行政处罚事先告知书");
        DOCUMENT_TYPE_KEYWORDS.put("STATEMENT_RECORD", "卖局\n陈述申辩记录");
        DOCUMENT_TYPE_KEYWORDS.put("STATEMENT_REVIEW_FORM", "卖局\n陈述申辩意见复核表");
        
        // 听证相关
        DOCUMENT_TYPE_KEYWORDS.put("HEARING_NOTICE", "卖局\n听证告知书");
        DOCUMENT_TYPE_KEYWORDS.put("HEARING_NOTIFICATION", "卖局\n听证通知书");
        DOCUMENT_TYPE_KEYWORDS.put("HEARING_REJECTION_NOTICE", "卖局\n不予受理听证通知书");
        DOCUMENT_TYPE_KEYWORDS.put("HEARING_ANNOUNCEMENT", "卖局\n听证公告");
        DOCUMENT_TYPE_KEYWORDS.put("HEARING_RECORD", "卖局\n听证笔录");
        DOCUMENT_TYPE_KEYWORDS.put("HEARING_REPORT", "卖局\n听证报告");
        
        // 决定和审批
        DOCUMENT_TYPE_KEYWORDS.put("CASE_APPROVAL_FORM", "卖局\n案件处理审批表");
        DOCUMENT_TYPE_KEYWORDS.put("CASE_DISCUSSION_RECORD", "卖局\n案件集体讨论记录");
        DOCUMENT_TYPE_KEYWORDS.put("EXTEND_DECISION_APPROVAL", "卖局\n延长作出行政处罚决定期限审批表");
        DOCUMENT_TYPE_KEYWORDS.put("EXTEND_DECISION_NOTICE", "卖局\n延长作出行政处罚决定期限告知书");
        DOCUMENT_TYPE_KEYWORDS.put("IMMEDIATE_PENALTY_DECISION", "卖局\n当场行政处罚决定书");
        DOCUMENT_TYPE_KEYWORDS.put("PENALTY_DECISION", "卖局\n行政处罚决定书");
        DOCUMENT_TYPE_KEYWORDS.put("ADMINISTRATIVE_DECISION", "卖局\n行政处理决定书");
        DOCUMENT_TYPE_KEYWORDS.put("NO_PENALTY_DECISION", "卖局\n不予行政处罚决定书");
        
        // 送达相关
        DOCUMENT_TYPE_KEYWORDS.put("ELECTRONIC_DELIVERY_CONFIRMATION", "卖局\n电子送达方式确认书");
        DOCUMENT_TYPE_KEYWORDS.put("DELIVERY_RECEIPT", "卖局\n送达回证");
        DOCUMENT_TYPE_KEYWORDS.put("DELIVERY_ANNOUNCEMENT", "卖局\n送达公告");
        
        // 后续处理
        DOCUMENT_TYPE_KEYWORDS.put("CORRECTION_NOTICE", "卖局\n责令改正通知书");
        DOCUMENT_TYPE_KEYWORDS.put("REVIEW_RECORD", "卖局\n复查记录表");
        DOCUMENT_TYPE_KEYWORDS.put("RECTIFICATION_NOTICE", "卖局\n整顿终结通知书");
        DOCUMENT_TYPE_KEYWORDS.put("DESTRUCTION_RECORD", "卖局\n销毁记录表");
        
        // 烟草专卖相关
        DOCUMENT_TYPE_KEYWORDS.put("TOBACCO_VALUE_CHANGE_APPROVAL", "卖局\n烟草专卖品变价处理审批表");
        DOCUMENT_TYPE_KEYWORDS.put("TOBACCO_TRANSFER_FORM", "卖局\n烟草专卖品移交单");
        
        // 强制执行相关
        DOCUMENT_TYPE_KEYWORDS.put("ENFORCEMENT_APPROVAL", "卖局\n行政强制执行事项审批表");
        DOCUMENT_TYPE_KEYWORDS.put("ADDITIONAL_FINE_DECISION", "卖局\n加处罚款决定书");
        DOCUMENT_TYPE_KEYWORDS.put("COMPLIANCE_NOTICE", "卖局\n行政处罚决定履行催告书");
        DOCUMENT_TYPE_KEYWORDS.put("ENFORCEMENT_APPLICATION", "卖局\n行政处罚强制执行申请书");
        
        // 罚款相关
        DOCUMENT_TYPE_KEYWORDS.put("FINE_PAYMENT_EXTENSION_APPROVAL", "卖局\n延期（分期）缴纳罚款审批表");
        DOCUMENT_TYPE_KEYWORDS.put("FINE_PAYMENT_EXTENSION_NOTICE", "卖局\n延期（分期）缴纳罚款通知书");
        
        // 其他文书
        DOCUMENT_TYPE_KEYWORDS.put("AWARD_REPORT_FORM", "卖局\n对协助办案有功个人、单位授奖呈报表");
        DOCUMENT_TYPE_KEYWORDS.put("CASE_WITHDRAWAL_REPORT", "卖局\n撤销立案报告表");
        DOCUMENT_TYPE_KEYWORDS.put("CASE_WITHDRAWAL_NOTICE", "卖局\n撤销立案通知书");
        DOCUMENT_TYPE_KEYWORDS.put("CASE_CLOSURE_REPORT", "卖局\n结案报告表");
        
        // 卷宗相关
        DOCUMENT_TYPE_KEYWORDS.put("CASE_COVER", "卖局\n卷宗封面");
        DOCUMENT_TYPE_KEYWORDS.put("CASE_DIRECTORY", "卖局\n卷宗目录");
        DOCUMENT_TYPE_KEYWORDS.put("CASE_REFERENCE_FORM", "卖局\n卷内备考表");
        
    }

    @Override
    public PdfSplitResultVO splitPdf(MultipartFile file, String caseUuid, Long caseId) {
        try {
            String validationError = validatePdfFile(file);
            if (validationError != null) {
                return PdfSplitResultVO.builder()
                    .success(false)
                    .message(validationError)
                    .build();
            }

            List<SplitDocumentVO> splitDocuments = performSplit(file, caseUuid, caseId);
            
            return PdfSplitResultVO.builder()
                .success(true)
                .message("PDF split successfully")
                .totalDocuments(splitDocuments.size())
                .splitDocuments(splitDocuments)
                .caseId(caseId != null ? caseId : 1L) // 如果传入caseId则使用传入值，否则默认为1
                .build();
                
        } catch (Exception e) {
            log.error("Error splitting PDF: {}", e.getMessage(), e);
            return PdfSplitResultVO.builder()
                .success(false)
                .message("Failed to split PDF: " + e.getMessage())
                .build();
        }
    }

    @Override
    public PdfSplitResultVO getSplitResult(String taskId) {
        return taskResults.get(taskId);
    }

    @Override
    public String splitPdfAsync(MultipartFile file, String caseUuid, Long caseId) {
        String taskId = UUID.randomUUID().toString();
        // TODO: Implement async processing
        // CompletableFuture.runAsync(() -> {
        //     PdfSplitResultVO result = splitPdf(file, caseUuid, caseId);
        //     taskResults.put(taskId, result);
        // });
        return taskId;
    }

    @Override
    public boolean deleteSplitResult(String taskId) {
        PdfSplitResultVO result = taskResults.remove(taskId);
        if (result != null && result.getSplitDocuments() != null) {
            // TODO: Delete files from MinIO
            for (SplitDocumentVO doc : result.getSplitDocuments()) {
                try {
                    // minioService.deleteFile(doc.getMinioUrl());
                    log.info("Would delete file: {}", doc.getMinioUrl());
                } catch (Exception e) {
                    log.error("Error deleting file: {}", e.getMessage());
                }
            }
        }
        return result != null;
    }

    @Override
    public String validatePdfFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return "File cannot be empty";
        }
        
        if (!"application/pdf".equals(file.getContentType())) {
            return "File must be PDF format";
        }
        
        if (file.getSize() > 50 * 1024 * 1024) { // 50MB limit
            return "File size cannot exceed 50MB";
        }
        
        try (PDDocument document = PDDocument.load(file.getInputStream())) {
            if (document.getNumberOfPages() == 0) {
                return "PDF file has no pages";
            }
        } catch (IOException e) {
            return "Invalid PDF file: " + e.getMessage();
        }
        
        return null;
    }

    /**
     * Perform PDF splitting
     * 增强的PDF拆分功能，支持多种文件类型判断策略
     */
    private List<SplitDocumentVO> performSplit(MultipartFile file, String caseUuid, Long caseId) throws IOException {
        List<SplitDocumentVO> splitDocuments = new ArrayList<>();
        
        // 生成任务ID
        String taskId = UUID.randomUUID().toString();
        
        try (PDDocument document = PDDocument.load(file.getInputStream())) {
            // 记录文件基本信息
            int totalPages = document.getNumberOfPages();
            log.info("开始处理PDF文件，总页数: {}，任务ID: {}", totalPages, taskId);
            
            // 生成缓存键并预先进行完整文档OCR识别
            String cacheKey = null;
            if (totalPages <= maxPagesForOcr) {
                cacheKey = generateCacheKey(document);
                log.info("开始预先进行完整文档OCR识别，缓存键: {}", cacheKey);
                
                // 检查缓存中是否已有结果
                if (!ocrResultCache.containsKey(cacheKey)) {
                    // 保存完整PDF文件用于OCR识别
                    String tempFileName = "temp_" + System.currentTimeMillis() + ".pdf";
                    Path tempDir = Paths.get("D:\\temp\\pdf");
                    Files.createDirectories(tempDir);
                    Path tempFilePath = tempDir.resolve(tempFileName);
                    document.save(tempFilePath.toFile());
                    
                    try {
                        // 进行完整文档OCR识别并缓存
                        String fullText = callPaddleOcrApiForFullDocument(tempFilePath);
                        ocrResultCache.put(cacheKey, fullText);
                        log.info("完整文档OCR识别完成并已缓存，文本长度: {}，缓存键: {}", fullText.length(), cacheKey);
                    } finally {
                        // 清理临时文件
                        try {
                            Files.deleteIfExists(tempFilePath);
                        } catch (IOException e) {
                            log.warn("Failed to delete temp file: {}", tempFilePath, e);
                        }
                    }
                } else {
                    log.info("使用已缓存的完整文档OCR结果，缓存键: {}", cacheKey);
                }
            }
            
            // 识别文档页面分组
            List<PageGroup> pageGroups = identifyDocumentPages(document, cacheKey);
            log.info("识别到{}个文档分组", pageGroups.size());
            
            // 统计文档类型分布
            Map<String, Integer> typeCount = new HashMap<>();
            for (PageGroup group : pageGroups) {
                typeCount.put(group.documentType, typeCount.getOrDefault(group.documentType, 0) + 1);
            }
            log.info("文档类型分布: {}", typeCount);
            
            for (int i = 0; i < pageGroups.size(); i++) {
                PageGroup group = pageGroups.get(i);
                log.info("处理第{}个文档分组: 类型={}, 页面范围={}-{}", 
                        i + 1, group.documentType, group.startPage, group.endPage);
                
                // 创建拆分文档
                SplitDocumentVO splitDoc = createSplitDocument(document, group, i + 1, taskId);
                splitDocuments.add(splitDoc);
                
                // 提取文本内容（使用缓存键）
                String extractedText = extractTextFromPages(document, group.startPage, group.endPage, cacheKey);
                
                // 二次验证文档类型（基于提取的完整文本）
                String verifiedType = detectDocumentType(extractedText);
                if (!verifiedType.equals(group.documentType)) {
                    log.info("文档类型二次验证: 原类型={}, 验证后类型={}", group.documentType, verifiedType);
                    // 更新文档类型
                    splitDoc.setDocumentType(verifiedType);
                    splitDoc.setDocumentTypeDesc(getDocumentTypeDesc(verifiedType));
                }
                
                // 特殊处理：如果是立案报告表，提取文书号并关联案件
                if ("CASE_FILING_REPORT".equals(verifiedType)) {
                    try {
                        CasesEntity relatedCase = processCaseFilingReport(extractedText);
                        if (relatedCase != null) {
                            log.info("立案报告表成功关联案件: 案件UUID={}, 案件名称={}", 
                                    relatedCase.getCaseUuid(), relatedCase.getCaseName());
                            // 可以在这里添加更多的关联逻辑，比如更新文档记录中的案件信息
                        }
                    } catch (Exception e) {
                        log.error("处理立案报告表时发生错误: {}", e.getMessage(), e);
                    }
                }
                
                // 创建识别记录
                RecognitionRecord record = createRecognitionRecord(splitDoc, extractedText, splitDoc.getMinioUrl(), taskId, caseId);
                       
                // 保存识别记录
                recognitionRecordService.save(record);
                
                // 启动异步内容识别
                asyncRecognizeContent(record);
                
                log.info("完成第{}个文档分组处理，文档ID: {}", i + 1, splitDoc.getDocumentId());
            }
            
            log.info("PDF拆分完成，共生成{}个文档", splitDocuments.size());
            
            // 清理缓存以释放内存
            if (cacheKey != null) {
                clearOcrCache(cacheKey);
            }
        }
        
        return splitDocuments;
    }

    /**
     * 根据内容识别文档页面
     * 拆分逻辑：从包含指定标题的当前页开始，直到包含另一个标题的页码减1页为止，这些页面都属于前一个标题类型的文件
     */
    private List<PageGroup> identifyDocumentPages(PDDocument document, String cacheKey) throws IOException {
        List<PageGroup> pageGroups = new ArrayList<>();
        
        int totalPages = document.getNumberOfPages();
        List<TitlePage> titlePages = new ArrayList<>();
        
        // 检查页数限制
        if (totalPages > maxPagesForOcr) {
            log.warn("PDF文件页数({})超过限制({}页)，跳过逐页OCR识别，将整个文档作为单一文档处理", totalPages, maxPagesForOcr);
            // 将整个文档作为OTHER_DOCUMENT处理
            PageGroup defaultGroup = new PageGroup();
            defaultGroup.startPage = 1;
            defaultGroup.endPage = totalPages;
            defaultGroup.documentType = "OTHER_DOCUMENT";
            pageGroups.add(defaultGroup);
            return pageGroups;
        }
        
        // 第一步：找出所有包含标题的页面
        for (int i = 1; i <= totalPages; i++) {
            // 使用PaddleOCR API提取单页文本（使用缓存键）
            String pageText = extractTextFromPages(document, i, i, cacheKey);
            
            // 调试输出：按页码打印页面内容
            log.info("=== 第{}页内容 ===", i);
            log.info("页面文本内容：\n{}", pageText);
            log.info("页面文本长度：{}", pageText.length());
            
            String detectedType = detectDocumentType(pageText);
            log.info("检测到的文档类型：{}", detectedType);
            
            if (!"OTHER_DOCUMENT".equals(detectedType)) {
                TitlePage titlePage = new TitlePage();
                titlePage.pageNumber = i;
                titlePage.documentType = detectedType;
                titlePages.add(titlePage);
                log.info("第{}页识别为标题页，文档类型：{}", i, detectedType);
            } else {
                log.info("第{}页未识别到标题", i);
            }
            log.info("=== 第{}页内容结束 ===\n", i);
        }
        
        // 第二步：根据标题页面创建文档组
        for (int i = 0; i < titlePages.size(); i++) {
            TitlePage currentTitle = titlePages.get(i);
            PageGroup group = new PageGroup();
            group.startPage = currentTitle.pageNumber;
            group.documentType = currentTitle.documentType;
            
            // 确定结束页面：下一个标题页面的前一页，或者文档的最后一页
            if (i < titlePages.size() - 1) {
                TitlePage nextTitle = titlePages.get(i + 1);
                group.endPage = nextTitle.pageNumber - 1;
            } else {
                group.endPage = totalPages;
            }
            
            // 确保页面范围有效
            if (group.endPage >= group.startPage) {
                pageGroups.add(group);
            }
        }
        
        // 如果没有找到任何标题页面，将整个文档作为OTHER_DOCUMENT处理
        if (pageGroups.isEmpty()) {
            PageGroup defaultGroup = new PageGroup();
            defaultGroup.startPage = 1;
            defaultGroup.endPage = totalPages;
            defaultGroup.documentType = "OTHER_DOCUMENT";
            pageGroups.add(defaultGroup);
        }
        
        return pageGroups;
    }

    /**
     * Detect document type based on content
     * 增强的文件类型检测，支持多种匹配策略
     */
    private String detectDocumentType(String text) {
        if (text == null || text.trim().isEmpty()) {
            log.warn("文本内容为空，无法进行文件类型检测");
            return "OTHER_DOCUMENT";
        }
        
        // 清理文本，移除多余的空白字符
        String cleanText = text.replaceAll("\\s+", " ").trim();
        log.debug("清理后的文本内容: {}", cleanText.substring(0, Math.min(cleanText.length(), 200)));
        
        // 按优先级进行文档类型检测
        Map<String, Integer> typeScores = new HashMap<>();
        
        for (Map.Entry<String, String> entry : DOCUMENT_TYPE_KEYWORDS.entrySet()) {
            String documentType = entry.getKey();
            String keyword = entry.getValue();
            
            // 精确匹配
            if (cleanText.contains(keyword)) {
                typeScores.put(documentType, typeScores.getOrDefault(documentType, 0) + 10);
                log.info("精确匹配到文档类型关键词: {} -> {}", keyword, documentType);
            }
            
            // 模糊匹配（去除标点符号）
            String normalizedText = cleanText.replaceAll("[\\p{Punct}\\s]+", "");
            String normalizedKeyword = keyword.replaceAll("[\\p{Punct}\\s]+", "");
            if (normalizedText.contains(normalizedKeyword)) {
                typeScores.put(documentType, typeScores.getOrDefault(documentType, 0) + 5);
                log.info("模糊匹配到文档类型关键词: {} -> {}", keyword, documentType);
            }
            
            // 部分匹配（关键词的主要部分）
            if (keyword.length() > 3) {
                String keywordCore = keyword.substring(0, keyword.length() - 1); // 去掉最后一个字符
                if (cleanText.contains(keywordCore)) {
                    typeScores.put(documentType, typeScores.getOrDefault(documentType, 0) + 3);
                    log.debug("部分匹配到文档类型关键词: {} -> {}", keywordCore, documentType);
                }
            }
        }
        
        // 选择得分最高的文档类型
        if (!typeScores.isEmpty()) {
            String bestType = typeScores.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("OTHER_DOCUMENT");
            
            int bestScore = typeScores.get(bestType);
            log.info("检测到文档类型: {}，得分: {}", bestType, bestScore);
            
            // 只有当得分足够高时才返回检测到的类型
            if (bestScore >= 5) {
                return bestType;
            }
        }
        
        log.info("未检测到明确的文档类型，归类为OTHER_DOCUMENT");
        return "OTHER_DOCUMENT";
    }

    /**
     * Create split document VO
     */
    private SplitDocumentVO createSplitDocument(PDDocument document, PageGroup group, int index, String taskId) {
        String documentType = group.documentType;
        String fileName = generateFileName(documentType, index, taskId);
        
        // Save split PDF
        String localFilePath = saveSplitPdf(document, group.startPage, group.endPage, fileName);
        
        // Upload to MinIO
        String minioUrl = uploadToMinio(localFilePath, fileName);
        
        return SplitDocumentVO.builder()
            .documentId(UUID.randomUUID().toString())
            .documentType(documentType)
            .documentTypeDesc(getDocumentTypeDesc(documentType))
            .fileName(fileName)
            .pageStart(group.startPage)
            .pageEnd(group.endPage)
            .pageCount(group.endPage - group.startPage + 1)
            .minioUrl(minioUrl)
            .localFilePath(localFilePath)
            .caseUuid(taskId)
            .createTime(LocalDateTime.now())
            .build();
    }

    /**
     * Get document type description
     */
    private String getDocumentTypeDesc(String documentType) {
        return DOCUMENT_TYPE_KEYWORDS.getOrDefault(documentType, "Unknown Document Type");
    }

    /**
     * Generate file name
     */
    private String generateFileName(String documentType, int index, String taskId) {
        String typeDesc = getDocumentTypeDesc(documentType);
        // 去掉"卖局\n"前缀
        if (typeDesc.startsWith("卖局\n")) {
            typeDesc = typeDesc.substring(3); // 去掉"卖局\n"（3个字符）
        }
        // 清理文件名中的非法字符，包括换行符、制表符等
        typeDesc = typeDesc.replaceAll("[\\r\\n\\t<>:\"/\\|?*]", "_");
        String timestamp = String.valueOf(System.currentTimeMillis());
        return String.format("%s_%s_%d_%s.pdf", taskId, typeDesc, index, timestamp);
    }

    /**
     * Save split PDF to local file
     */
    private String saveSplitPdf(PDDocument originalDoc, int startPage, int endPage, String fileName) {
        try {
            PDDocument splitDoc = new PDDocument();
            
            for (int i = startPage - 1; i < endPage; i++) {
                PDPage page = originalDoc.getPage(i);
                splitDoc.addPage(page);
            }
            
            // 使用系统临时目录，兼容Windows和Linux
            Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"));
            Path filePath = tempDir.resolve(fileName);
            
            // 确保目录存在
            Files.createDirectories(tempDir);
            
            splitDoc.save(filePath.toFile());
            splitDoc.close();
            
            log.info("拆分PDF文件已保存到: {}", filePath.toString());
            return filePath.toString();
        } catch (IOException e) {
            log.error("Error saving split PDF: {}", e.getMessage(), e);
            // 返回一个默认路径而不是null，避免数据库插入失败
            String defaultPath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
            log.warn("保存PDF失败，返回默认路径: {}", defaultPath);
            return defaultPath;
        }
    }

    /**
     * Upload file to MinIO
     */
    private String uploadToMinio(String localFilePath, String fileName) {
        try {
            // 读取本地文件
            byte[] fileBytes = Files.readAllBytes(Paths.get(localFilePath));
            
            // 上传文件到MinIO
            BladeFile bladeFile = ossBuilder.template().putFile(fileName, new ByteArrayInputStream(fileBytes));
            
            // 删除本地临时文件
            try {
                Files.deleteIfExists(Paths.get(localFilePath));
                log.info("删除本地临时文件: {}", localFilePath);
            } catch (IOException e) {
                log.warn("删除本地临时文件失败: {}", localFilePath, e);
            }
            
            log.info("文件 {} 成功上传到MinIO，URL: {}", fileName, bladeFile.getLink());
            return bladeFile.getLink();
        } catch (Exception e) {
            log.error("上传文件到MinIO失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Extract text from specified pages using PaddleOCR API with caching
     */
    private String extractTextFromPages(PDDocument document, int startPage, int endPage) {
        return extractTextFromPages(document, startPage, endPage, null);
    }
    
    /**
     * Extract text from specified pages using PaddleOCR API with caching
     * @param document PDF文档
     * @param startPage 起始页码
     * @param endPage 结束页码
     * @param cacheKey 缓存键，如果为null则生成默认键
     */
    private String extractTextFromPages(PDDocument document, int startPage, int endPage, String cacheKey) {
        try {
            // 检查页数限制
            int totalPages = document.getNumberOfPages();
            if (totalPages > maxPagesForOcr) {
                log.warn("PDF文件页数({})超过限制({}页)，跳过OCR识别，直接使用传统文本提取", totalPages, maxPagesForOcr);
                return extractTextFromPagesLegacy(document, startPage, endPage);
            }
            
            // 生成缓存键
            if (cacheKey == null) {
                cacheKey = generateCacheKey(document);
            }
            
            // 检查缓存
            String cachedResult = ocrResultCache.get(cacheKey);
            if (cachedResult != null) {
                log.info("使用缓存的OCR结果，缓存键: {}", cacheKey);
                String extractedText = extractTextFromFullOcrResultByPageRange(cachedResult, startPage, endPage);
                log.info("从缓存中提取第{}-{}页文本，长度: {}", startPage, endPage, extractedText.length());
                return extractedText;
            }
            
            // 缓存中没有结果，需要进行OCR识别
            log.info("缓存中未找到OCR结果，开始进行完整文档OCR识别，缓存键: {}", cacheKey);
            
            // 保存完整PDF文件用于OCR识别
            String tempFileName = "temp_" + System.currentTimeMillis() + ".pdf";
            Path tempDir = Paths.get("D:\\temp\\pdf");
            // 确保目录存在
            Files.createDirectories(tempDir);
            Path tempFilePath = tempDir.resolve(tempFileName);
            document.save(tempFilePath.toFile());
            log.info("临时PDF文件已保存到: {}，总页数: {}", tempFilePath, totalPages);
            
            try {
                // 使用PaddleOCR API识别完整文件
                String fullText = callPaddleOcrApiForFullDocument(tempFilePath);
                
                // 将结果存入缓存
                ocrResultCache.put(cacheKey, fullText);
                log.info("OCR识别完成，结果已缓存，缓存键: {}，文本长度: {}", cacheKey, fullText.length());
                
                // 提取指定页面范围的文本
                String extractedText = extractTextFromFullOcrResultByPageRange(fullText, startPage, endPage);
                log.info("Extract text from pages {}-{} using PaddleOCR, length: {}", startPage, endPage, extractedText.length());
                return extractedText;
                
            } finally {
                // 清理临时文件
                try {
                    Files.deleteIfExists(tempFilePath);
                } catch (IOException e) {
                    log.warn("Failed to delete temp file: {}", tempFilePath, e);
                }
            }
            
        } catch (IOException e) {
            log.error("Failed to extract PDF text using OCR API: {}", e.getMessage(), e);
            // 降级到原始方法
            return extractTextFromPagesLegacy(document, startPage, endPage);
        }
    }

    /**
     * 清理OCR缓存
     * @param cacheKey 要清理的缓存键，如果为null则清理所有缓存
     */
    private void clearOcrCache(String cacheKey) {
        if (cacheKey != null) {
            String removedResult = ocrResultCache.remove(cacheKey);
            if (removedResult != null) {
                log.info("已清理OCR缓存，缓存键: {}，释放内存大小: {} 字符", cacheKey, removedResult.length());
            } else {
                log.debug("缓存键不存在，无需清理: {}", cacheKey);
            }
        } else {
            int cacheSize = ocrResultCache.size();
            ocrResultCache.clear();
            log.info("已清理所有OCR缓存，共清理 {} 个缓存项", cacheSize);
        }
    }
    
    /**
     * 生成PDF文档的缓存键
     * 基于文档的页数、文件大小等特征生成唯一标识
     */
    private String generateCacheKey(PDDocument document) {
        try {
            int pageCount = document.getNumberOfPages();
            // 获取文档的一些基本信息作为缓存键的组成部分
            StringBuilder keyBuilder = new StringBuilder();
            keyBuilder.append("pdf_");
            keyBuilder.append(pageCount);
            keyBuilder.append("_");
            
            // 如果可能，添加文档的创建时间或修改时间
            if (document.getDocumentInformation() != null) {
                if (document.getDocumentInformation().getCreationDate() != null) {
                    keyBuilder.append(document.getDocumentInformation().getCreationDate().getTimeInMillis());
                } else if (document.getDocumentInformation().getModificationDate() != null) {
                    keyBuilder.append(document.getDocumentInformation().getModificationDate().getTimeInMillis());
                }
            }
            
            // 添加当前时间戳作为会话级别的标识
            keyBuilder.append("_").append(System.currentTimeMillis() / 1000 / 3600); // 小时级别的时间戳
            
            String cacheKey = keyBuilder.toString();
            log.debug("生成缓存键: {}", cacheKey);
            return cacheKey;
            
        } catch (Exception e) {
            log.warn("生成缓存键失败，使用默认键: {}", e.getMessage());
            return "pdf_default_" + System.currentTimeMillis();
        }
    }
    
    /**
     * 从完整OCR识别结果中提取指定页面范围的文本
     */
    private String extractTextFromFullOcrResultByPageRange(String fullOcrResult, int startPage, int endPage) {
        try {
            // 如果是简单的文本格式，直接返回
            if (!fullOcrResult.contains("=== 第") || !fullOcrResult.contains("页 ===")) {
                log.info("OCR结果为简单文本格式，直接返回完整内容");
                return fullOcrResult;
            }
            
            // 按页面分割文本
            String[] lines = fullOcrResult.split("\n");
            StringBuilder pageRangeText = new StringBuilder();
            boolean inTargetRange = false;
            
            for (String line : lines) {
                // 检查是否是页面标记行
                if (line.matches("=== 第\\d+页 ===")) {
                    // 提取页码
                    String pageNumStr = line.replaceAll("[^\\d]", "");
                    if (!pageNumStr.isEmpty()) {
                        int pageNum = Integer.parseInt(pageNumStr);
                        
                        // 判断是否在目标页面范围内
                        if (pageNum >= startPage && pageNum <= endPage) {
                            inTargetRange = true;
                            // 不包含页面标记行本身
                        } else {
                            inTargetRange = false;
                        }
                    }
                } else if (inTargetRange) {
                    // 在目标页面范围内，添加文本内容
                    pageRangeText.append(line).append("\n");
                }
            }
            
            String result = pageRangeText.toString().trim();
            log.info("从完整OCR结果中提取第{}-{}页文本，长度: {}", startPage, endPage, result.length());
            return result;
            
        } catch (Exception e) {
            log.error("从完整OCR结果中提取第{}-{}页文本失败: {}", startPage, endPage, e.getMessage(), e);
            // 如果提取失败，返回完整文本
            return fullOcrResult;
        }
    }

    
    
    /**
     * 从真实API响应中提取文本
     */
    private String extractTextFromApiResponse(JsonNode responseJson) {
        try {
            JsonNode dataNode = responseJson.get("data");
            if (dataNode == null) {
                log.warn("API响应中未找到data节点");
                return "";
            }
            
            // 检查是否有full_text字段（整体文本）
            if (dataNode.has("full_text")) {
                String fullText = dataNode.get("full_text").asText();
                log.info("从API响应中提取到完整文本，长度: {}", fullText.length());
                return fullText;
            }
            
            // 如果没有full_text，则从page_results中提取
            if (dataNode.has("page_results")) {
                StringBuilder fullText = new StringBuilder();
                JsonNode pageResultsNode = dataNode.get("page_results");
                
                for (JsonNode pageNode : pageResultsNode) {
                    int pageNum = pageNode.get("page_num").asInt();
                    fullText.append("=== 第").append(pageNum).append("页 ===").append("\n");
                    
                    if (pageNode.has("full_text")) {
                        // 使用页面的full_text
                        fullText.append(pageNode.get("full_text").asText()).append("\n");
                    } else if (pageNode.has("text_blocks")) {
                        // 从text_blocks中拼接文本
                        JsonNode textBlocksNode = pageNode.get("text_blocks");
                        for (JsonNode blockNode : textBlocksNode) {
                            if (blockNode.has("text")) {
                                String text = blockNode.get("text").asText();
                                if (!text.trim().isEmpty()) {
                                    fullText.append(text).append("\n");
                                }
                            }
                        }
                    }
                }
                
                String result = fullText.toString().trim();
                log.info("从API响应的page_results中提取到文本，总页数: {}，文本长度: {}", 
                        pageResultsNode.size(), result.length());
                return result;
            }
            
            log.warn("API响应中未找到预期的文本数据结构");
            return "";
            
        } catch (Exception e) {
            log.error("从API响应中提取文本失败: {}", e.getMessage(), e);
            return "";
        }
    }
    
    /**
     * 调用PaddleOCR API进行完整文档的文本提取
     * 根据配置选择使用真实API或模拟数据
     */
    private String callPaddleOcrApiForFullDocument(Path pdfFilePath) throws IOException {
        if (useMockData) {
            log.info("使用模拟数据进行完整文档文本提取");
            return callPaddleOcrApiWithMockData(pdfFilePath);
        } else {
            log.info("使用真实PaddleOCR API进行完整文档文本提取");
            return callPaddleOcrApiReal(pdfFilePath);
        }
    }
    
    /**
     * 使用模拟数据进行完整文档文本提取
     * 支持多种测试类型的文件类型判断
     */
    private String callPaddleOcrApiWithMockData(Path pdfFilePath) throws IOException {
        try {
            // 使用本地模拟数据文件
            String testDataPath = "d:/xiangm/惠州烟草/code/hzyc-backend/test_results.json";
            log.info("使用模拟数据文件: {}", testDataPath);
            
            // 读取模拟数据文件
            String mockResponseBody = Files.readString(Paths.get(testDataPath), StandardCharsets.UTF_8);
            
            // 解析模拟响应数据
            JsonNode mockData = objectMapper.readTree(mockResponseBody);
            
            // 尝试多种测试类型进行文件类型判断
            String[] testTypes = {"basic_test", "no_angle_cls_test", "page_range_test"};
            
            for (String testType : testTypes) {
                JsonNode testData = mockData.get(testType);
                if (testData != null && testData.get("success").asBoolean()) {
                    log.info("使用{}进行完整文档文本提取", testType);
                    
                    String extractedText = extractTextFromTestData(testData, testType);
                    if (!extractedText.isEmpty()) {
                        return extractedText;
                    }
                }
            }
            
            log.warn("所有测试类型都未能成功提取完整文档文本");
            
        } catch (Exception e) {
            log.error("使用模拟数据提取完整文档文本失败: {}", e.getMessage(), e);
            throw new IOException("模拟数据文本提取失败", e);
        }
        
        return "";
    }
    
    /**
     * 调用真实的PaddleOCR API进行完整文档文本提取
     */
    private String callPaddleOcrApiReal(Path pdfFilePath) throws IOException {
        try {
            // 准备请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            // 创建文件资源
            byte[] fileBytes = Files.readAllBytes(pdfFilePath);
            ByteArrayResource fileResource = new ByteArrayResource(fileBytes) {
                @Override
                public String getFilename() {
                    return pdfFilePath.getFileName().toString();
                }
            };
            
            // 构建请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", fileResource);
            body.add("use_angle_cls", "true");
            body.add("use_dilation", "true");
            
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            
            // 发送请求
            String apiUrl = paddleocrApiUrl + "/ocr/pdf";
            log.info("调用PaddleOCR API提取完整文档: {}", apiUrl);
            
            ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, requestEntity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.info("PaddleOCR API调用成功，响应长度: {}", responseBody != null ? responseBody.length() : 0);
                
                // 解析响应
                JsonNode responseJson = objectMapper.readTree(responseBody);
                if (responseJson.get("success").asBoolean()) {
                    return extractTextFromApiResponse(responseJson);
                } else {
                    String errorMsg = responseJson.has("message") ? responseJson.get("message").asText() : "未知错误";
                    throw new IOException("PaddleOCR API返回错误: " + errorMsg);
                }
            } else {
                throw new IOException("PaddleOCR API调用失败，状态码: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("调用真实PaddleOCR API提取完整文档失败: {}", e.getMessage(), e);
            throw new IOException("PaddleOCR API调用失败", e);
        }
    }
    

    
    /**
     * 从测试数据中提取文本内容
     */
    private String extractTextFromTestData(JsonNode testData, String testType) {
        try {
            JsonNode dataNode = testData.get("data");
            if (dataNode == null) {
                log.warn("{}中未找到data节点", testType);
                return "";
            }
            
            // 检查是否有full_text字段（整体文本）
            if (dataNode.has("full_text")) {
                String fullText = dataNode.get("full_text").asText();
                log.info("从{}的API响应中提取到完整文本，长度: {}", testType, fullText.length());
                return fullText;
            }
            
            // 如果没有full_text，则从page_results中提取
            if (dataNode.has("page_results")) {
                StringBuilder fullText = new StringBuilder();
                JsonNode pageResultsNode = dataNode.get("page_results");
                
                for (JsonNode pageNode : pageResultsNode) {
                    int pageNum = pageNode.get("page_num").asInt();
                    fullText.append("=== 第").append(pageNum).append("页 ===\n");
                    
                    if (pageNode.has("full_text")) {
                        // 使用页面的full_text
                        fullText.append(pageNode.get("full_text").asText()).append("\n");
                    } else if (pageNode.has("text_blocks")) {
                        // 从text_blocks中拼接文本
                        JsonNode textBlocksNode = pageNode.get("text_blocks");
                        for (JsonNode blockNode : textBlocksNode) {
                            if (blockNode.has("text")) {
                                String text = blockNode.get("text").asText();
                                if (!text.trim().isEmpty()) {
                                    fullText.append(text).append("\n");
                                }
                            }
                        }
                    }
                }
                
                String result = fullText.toString().trim();
                log.info("从{}的page_results中提取到文本，总页数: {}，文本长度: {}", 
                        testType, pageResultsNode.size(), result.length());
                return result;
            }
            
            log.warn("{}中未找到预期的文本数据结构", testType);
            return "";
            
        } catch (Exception e) {
            log.error("从{}中提取文本失败: {}", testType, e.getMessage(), e);
            return "";
        }
    }
    

    
    /**
     * 原始的PDF文本提取方法（降级使用）
     */
    private String extractTextFromPagesLegacy(PDDocument document, int startPage, int endPage) {
        try {
            PDFTextStripper stripper = new PDFTextStripper();
            stripper.setStartPage(startPage);
            stripper.setEndPage(endPage);
            String text = stripper.getText(document);
            log.info("Extract text from pages {}-{} using legacy method, length: {}", startPage, endPage, text.length());
            return text;
        } catch (IOException e) {
            log.error("Failed to extract PDF text using legacy method: {}", e.getMessage(), e);
            return "";
        }
    }


    
    /**
     * Create recognition record
     */
    private RecognitionRecord createRecognitionRecord(SplitDocumentVO splitDoc, String extractedText, String minioUrl, String taskId, Long caseId) {
        RecognitionRecord record = new RecognitionRecord();
        record.setTaskId(taskId);
        record.setOriginalFileName(splitDoc.getFileName());
        record.setSplitFileName(splitDoc.getFileName()); // 设置拆分后文件名
        record.setDocumentType(splitDoc.getDocumentType());
        
        // 确保filePath字段不为null
        String filePath = splitDoc.getLocalFilePath();
        if (filePath == null || filePath.trim().isEmpty()) {
            // 如果本地文件路径为空，使用MinIO URL作为备选
            filePath = minioUrl != null ? minioUrl : "";
            log.warn("本地文件路径为空，使用MinIO URL作为文件路径: {}", filePath);
        }
        record.setFilePath(filePath); // 设置文件存储路径
        
        record.setPageStart(splitDoc.getPageStart());
        record.setPageEnd(splitDoc.getPageEnd());
        record.setPageCount(splitDoc.getPageCount()); // 设置页数
        record.setMinioUrl(minioUrl);
        record.setRecognitionStatus("PENDING");
        record.setExtractedContent(extractedText);
        record.setCaseId(caseId); // 设置案件ID
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        return record;
    }

    /**
     * Async content recognition
     */
    private void asyncRecognizeContent(RecognitionRecord record) {
        try {
            // TODO: Implement async OCR or content recognition
            // CompletableFuture.runAsync(() -> {
            //     // Perform OCR or content analysis
            //     // Update recognition record with results
            //     recognitionRecordService.recognizeAndSaveContent(record.getId(), extractedContent);
            //     recognitionRecordService.linkCaseByName(record.getId(), caseName);
            // });
            
            log.info("Async recognition task started, record ID: {}", record.getId());
        } catch (Exception e) {
            log.error("Failed to start async recognition task: {}", e.getMessage(), e);
        }
    }

    /**
     * 标题页面信息
     */
    private static class TitlePage {
        private int pageNumber;
        private String documentType;
    }
    
    /**
     * 文档拆分的页面组
     */
    private static class PageGroup {
        private int startPage;
        private int endPage;
        private String documentType;
        
        // Getters and setters would be here
    }

    /**
     * 从文档内容中提取文书号
     * 支持格式："博烟立﹝2025﹞第16号"、"惠阳烟立﹝2025﹞第14号"、"博烟立﹝2025﹞第83号"
     * 
     * @param documentContent 文档内容
     * @return 提取到的文书号，如果未找到则返回null
     */
    public String extractDocumentNumber(String documentContent) {
        if (documentContent == null || documentContent.trim().isEmpty()) {
            log.warn("文档内容为空，无法提取文书号");
            return null;
        }

        // 文书号正则表达式模式
        // 匹配格式：地区名+烟立+年份+第+数字+号
        // 支持全角和半角括号
        String[] patterns = {
            // 全角括号：博烟立﹝2025﹞第16号
            "([\\u4e00-\\u9fa5]+烟立)﹝(\\d{4})﹞第(\\d+)号",
            // 半角括号：博烟立[2025]第16号  
            "([\\u4e00-\\u9fa5]+烟立)\\[(\\d{4})\\]第(\\d+)号",
            // 中文括号：博烟立（2025）第16号
            "([\\u4e00-\\u9fa5]+烟立)（(\\d{4})）第(\\d+)号",
            // 英文括号：博烟立(2025)第16号
            "([\\u4e00-\\u9fa5]+烟立)\\((\\d{4})\\)第(\\d+)号"
        };

        for (String patternStr : patterns) {
            Pattern pattern = Pattern.compile(patternStr);
            Matcher matcher = pattern.matcher(documentContent);
            
            if (matcher.find()) {
                String fullMatch = matcher.group(0);
                log.info("成功提取文书号: {}", fullMatch);
                return fullMatch;
            }
        }

        log.warn("未能从文档内容中提取到文书号");
        return null;
    }

    /**
     * 根据文书号查询案件记录
     * 
     * @param documentNumber 文书号
     * @return 匹配的案件记录，如果未找到则返回null
     */
    public CasesEntity findCaseByDocumentNumber(String documentNumber) {
        if (documentNumber == null || documentNumber.trim().isEmpty()) {
            log.warn("文书号为空，无法查询案件记录");
            return null;
        }

        try {
            // 使用QueryWrapper查询ca_case表中的reg_doc_no字段
            QueryWrapper<CasesEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("reg_doc_no", documentNumber.trim());
            
            CasesEntity caseEntity = casesService.getOne(queryWrapper);
            
            if (caseEntity != null) {
                log.info("成功找到匹配的案件记录: 案件UUID={}, 案件名称={}, 文书号={}", 
                        caseEntity.getCaseUuid(), caseEntity.getCaseName(), documentNumber);
                return caseEntity;
            } else {
                log.warn("未找到文书号为 {} 的案件记录", documentNumber);
                return null;
            }
        } catch (Exception e) {
            log.error("查询案件记录时发生错误，文书号: {}, 错误信息: {}", documentNumber, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理立案报告表文档，提取文书号并关联案件
     * 当识别到CASE_FILING_REPORT文档类型时调用此方法
     * 
     * @param documentContent 文档内容
     * @return 关联的案件记录，如果未找到则返回null
     */
    public CasesEntity processCaseFilingReport(String documentContent) {
        log.info("开始处理立案报告表文档，提取文书号并关联案件");
        
        // 1. 从文档内容中提取文书号
        String documentNumber = extractDocumentNumber(documentContent);
        if (documentNumber == null) {
            log.warn("立案报告表中未能提取到文书号");
            return null;
        }
        
        // 2. 根据文书号查询案件记录
        CasesEntity caseEntity = findCaseByDocumentNumber(documentNumber);
        if (caseEntity == null) {
            log.warn("未找到文书号 {} 对应的案件记录", documentNumber);
            return null;
        }
        
        log.info("成功处理立案报告表文档，文书号: {}，关联案件: {}", documentNumber, caseEntity.getCaseName());
        return caseEntity;
    }
}