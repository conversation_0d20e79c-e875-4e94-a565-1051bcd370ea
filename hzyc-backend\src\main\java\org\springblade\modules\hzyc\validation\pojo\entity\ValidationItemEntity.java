/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hzyc.validation.pojo.entity;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 校验项详情表 实体类
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Data
@TableName("ca_validation_item")
@Schema(description = "ValidationItem对象")
@EqualsAndHashCode(callSuper = true)
public class ValidationItemEntity extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 校验结果ID，关联ca_validation_result表
	 */
	@Schema(description = "校验结果ID")
	private Long validationResultId;

	/**
	 * 校验类别
	 */
	@Schema(description = "校验类别")
	private String category;

	/**
	 * 校验项描述
	 */
	@Schema(description = "校验项描述")
	private String description;

	/**
	 * 扣分
	 */
	@Schema(description = "扣分")
	private BigDecimal deductedScore;

	/**
	 * 校验状态：PASSED-通过，FAILED-失败
	 */
//	@Schema(description = "校验状态")
//	private String status;

	/**
	 * Agent类型标识
	 */
	@Schema(description = "Agent类型标识")
	private String agentType;

	/**
	 * 详细信息
	 */
	@Schema(description = "详细信息")
	private String detail;

	/**
	 * 建议
	 */
	@Schema(description = "建议")
	private String suggestion;

	/**
	 * 文档位置信息
	 */
	@Schema(description = "文档位置信息")
	private String documentLocation;

}