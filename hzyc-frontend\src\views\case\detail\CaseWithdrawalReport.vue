<template>
  <div class="case-cancellation-report-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>撤销立案报告表</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.tobacco_bureau_name"
                placeholder="烟草专卖局名称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>撤销立案报告表</h2>
            </div>

            <div class="document-number">
              <span>烟撤〔</span>
              <el-input
                v-model="formData.doc_year"
                placeholder="年份"
                style="width: 80px;"
              />
              <span>〕第</span>
              <el-input
                v-model="formData.doc_serial_number"
                placeholder="序号"
                style="width: 80px;"
              />
              <span>号</span>
            </div>
          </div>

          <!-- 案件基本信息 -->
          <div class="content-section">
            <div class="form-row">
              <label class="form-label">案由：</label>
              <el-input
                v-model="formData.case_reason"
                placeholder="案由"
                class="form-input"
              />
            </div>

            <div class="form-row">
              <label class="form-label">案件来源：</label>
              <el-input
                v-model="formData.case_source"
                placeholder="案件来源"
                class="form-input"
                style="width: 200px;"
              />
              <label class="form-label" style="margin-left: 20px;">立案编号：</label>
              <el-input
                v-model="formData.register_number"
                placeholder="立案编号"
                class="form-input"
                style="width: 200px;"
              />
            </div>

            <div class="form-row">
              <label class="form-label">案发时间：</label>
              <el-input
                v-model="formData.case_date"
                placeholder="案发时间"
                class="form-input"
                style="width: 200px;"
              />
              <label class="form-label" style="margin-left: 20px;">案发地点：</label>
              <el-input
                v-model="formData.case_addr"
                placeholder="案发地点"
                class="form-input"
                style="width: 300px;"
              />
            </div>
          </div>

          <!-- 当事人信息 - 单位 -->
          <div class="content-section">
            <h4>当事人信息（单位）</h4>
            <div class="form-row">
              <label class="form-label">名称：</label>
              <el-input
                v-model="formData.company_name"
                placeholder="单位名称"
                class="form-input"
                style="width: 300px;"
              />
              <label class="form-label" style="margin-left: 20px;">联系电话：</label>
              <el-input
                v-model="formData.company_phone"
                placeholder="联系电话"
                class="form-input"
                style="width: 200px;"
              />
            </div>

            <div class="form-row">
              <label class="form-label">法定代表人（负责人）：</label>
              <el-input
                v-model="formData.legal_representative"
                placeholder="法定代表人"
                class="form-input"
                style="width: 200px;"
              />
            </div>

            <div class="form-row">
              <label class="form-label">地址：</label>
              <el-input
                v-model="formData.company_address"
                placeholder="单位地址"
                class="form-input"
                style="width: 400px;"
              />
            </div>
          </div>

          <!-- 当事人信息 - 个人 -->
          <div class="content-section">
            <h4>当事人信息（个人/个体工商户）</h4>
            <div class="form-row">
              <label class="form-label">姓名：</label>
              <el-input
                v-model="formData.person_name"
                placeholder="姓名"
                class="form-input"
                style="width: 150px;"
              />
              <label class="form-label" style="margin-left: 20px;">性别：</label>
              <el-select
                v-model="formData.person_gender"
                placeholder="性别"
                style="width: 100px;"
              >
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
              <label class="form-label" style="margin-left: 20px;">年龄：</label>
              <el-input
                v-model="formData.person_age"
                placeholder="年龄"
                class="form-input"
                style="width: 80px;"
              />
              <label class="form-label" style="margin-left: 20px;">民族：</label>
              <el-input
                v-model="formData.person_nationality"
                placeholder="民族"
                class="form-input"
                style="width: 100px;"
              />
            </div>

            <div class="form-row">
              <label class="form-label">证件类型及号码：</label>
              <el-input
                v-model="formData.id_type_and_number"
                placeholder="证件类型及号码"
                class="form-input"
                style="width: 300px;"
              />
              <label class="form-label" style="margin-left: 20px;">联系电话：</label>
              <el-input
                v-model="formData.person_phone"
                placeholder="联系电话"
                class="form-input"
                style="width: 200px;"
              />
            </div>

            <div class="form-row">
              <label class="form-label">住址：</label>
              <el-input
                v-model="formData.person_address"
                placeholder="住址"
                class="form-input"
                style="width: 400px;"
              />
            </div>
          </div>

          <!-- 案情摘要 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <label class="form-label">案情摘要：</label>
              <el-input
                v-model="formData.case_summary"
                type="textarea"
                :autosize="{ minRows: 4 }"
                placeholder="案情摘要"
                class="case-summary auto-resize-textarea"
                maxlength="2000"
                show-word-limit
              />
            </div>
          </div>

          <!-- 承办人意见 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <label class="form-label">承办人意见：</label>
              <el-input
                v-model="formData.undertaker_opinion"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="承办人意见"
                class="undertaker-opinion auto-resize-textarea"
                maxlength="1500"
                show-word-limit
              />
            </div>
            <div class="signature-section">
              <div class="signature-line">
                <span>签名：</span>
                <el-input
                  v-model="formData.undertaker_name"
                  placeholder="承办人姓名"
                  style="width: 150px;"
                />
                <span style="margin-left: 20px;">日期：</span>
                <el-input
                  v-model="formData.undertaker_sign_year"
                  placeholder="年"
                  style="width: 80px;"
                />
                <span>年</span>
                <el-input
                  v-model="formData.undertaker_sign_month"
                  placeholder="月"
                  style="width: 60px;"
                />
                <span>月</span>
                <el-input
                  v-model="formData.undertaker_sign_day"
                  placeholder="日"
                  style="width: 60px;"
                />
                <span>日</span>
              </div>
            </div>
          </div>

          <!-- 承办部门意见 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <label class="form-label">承办部门意见：</label>
              <el-input
                v-model="formData.department_opinion"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="承办部门意见"
                class="department-opinion auto-resize-textarea"
                maxlength="1500"
                show-word-limit
              />
            </div>
            <div class="signature-section">
              <div class="signature-line">
                <span>签名：</span>
                <el-input
                  v-model="formData.department_leader_name"
                  placeholder="部门负责人姓名"
                  style="width: 150px;"
                />
                <span style="margin-left: 20px;">日期：</span>
                <el-input
                  v-model="formData.department_sign_year"
                  placeholder="年"
                  style="width: 80px;"
                />
                <span>年</span>
                <el-input
                  v-model="formData.department_sign_month"
                  placeholder="月"
                  style="width: 60px;"
                />
                <span>月</span>
                <el-input
                  v-model="formData.department_sign_day"
                  placeholder="日"
                  style="width: 60px;"
                />
                <span>日</span>
              </div>
            </div>
          </div>

          <!-- 负责人意见 -->
          <div class="content-section">
            <div class="textarea-wrapper">
              <label class="form-label">负责人意见：</label>
              <el-input
                v-model="formData.supervisor_opinion"
                type="textarea"
                :autosize="{ minRows: 3 }"
                placeholder="负责人意见"
                class="supervisor-opinion auto-resize-textarea"
                maxlength="1500"
                show-word-limit
              />
            </div>
            <div class="signature-section">
              <div class="signature-line">
                <span>签名：</span>
                <el-input
                  v-model="formData.supervisor_name"
                  placeholder="负责人姓名"
                  style="width: 150px;"
                />
                <span style="margin-left: 20px;">日期：</span>
                <el-input
                  v-model="formData.supervisor_sign_year"
                  placeholder="年"
                  style="width: 80px;"
                />
                <span>年</span>
                <el-input
                  v-model="formData.supervisor_sign_month"
                  placeholder="月"
                  style="width: 60px;"
                />
                <span>月</span>
                <el-input
                  v-model="formData.supervisor_sign_day"
                  placeholder="日"
                  style="width: 60px;"
                />
                <span>日</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  tobacco_bureau_name: '',
  doc_year: '',
  doc_serial_number: '',
  case_reason: '',
  case_source: '',
  register_number: '',
  case_date: '',
  case_addr: '',
  company_name: '',
  company_phone: '',
  legal_representative: '',
  company_address: '',
  person_name: '',
  person_gender: '',
  person_age: '',
  person_nationality: '',
  id_type_and_number: '',
  person_phone: '',
  person_address: '',
  case_summary: '',
  undertaker_opinion: '',
  undertaker_name: '',
  undertaker_sign_year: '',
  undertaker_sign_month: '',
  undertaker_sign_day: '',
  department_opinion: '',
  department_leader_name: '',
  department_sign_year: '',
  department_sign_month: '',
  department_sign_day: '',
  supervisor_opinion: '',
  supervisor_name: '',
  supervisor_sign_year: '',
  supervisor_sign_month: '',
  supervisor_sign_day: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      tobacco_bureau_name: docContent.tobacco_bureau_name || newVal.tobacco_bureau_name || '',
      doc_year: docContent.doc_year || newVal.doc_year || '',
      doc_serial_number: docContent.doc_serial_number || newVal.doc_serial_number || '',
      case_reason: docContent.case_reason || newVal.case_reason || '',
      case_source: docContent.case_source || newVal.case_source || '',
      register_number: docContent.register_number || newVal.register_number || '',
      case_date: docContent.case_date || newVal.case_date || '',
      case_addr: docContent.case_addr || newVal.case_addr || '',
      company_name: docContent.company_name || newVal.company_name || '',
      company_phone: docContent.company_phone || newVal.company_phone || '',
      legal_representative: docContent.legal_representative || newVal.legal_representative || '',
      company_address: docContent.company_address || newVal.company_address || '',
      person_name: docContent.person_name || newVal.person_name || '',
      person_gender: docContent.person_gender || newVal.person_gender || '',
      person_age: docContent.person_age || newVal.person_age || '',
      person_nationality: docContent.person_nationality || newVal.person_nationality || '',
      id_type_and_number: docContent.id_type_and_number || newVal.id_type_and_number || '',
      person_phone: docContent.person_phone || newVal.person_phone || '',
      person_address: docContent.person_address || newVal.person_address || '',
      case_summary: docContent.case_summary || newVal.case_summary || '',
      undertaker_opinion: docContent.undertaker_opinion || newVal.undertaker_opinion || '',
      undertaker_name: docContent.undertaker_name || newVal.undertaker_name || '',
      undertaker_sign_year: docContent.undertaker_sign_year || newVal.undertaker_sign_year || '',
      undertaker_sign_month: docContent.undertaker_sign_month || newVal.undertaker_sign_month || '',
      undertaker_sign_day: docContent.undertaker_sign_day || newVal.undertaker_sign_day || '',
      department_opinion: docContent.department_opinion || newVal.department_opinion || '',
      department_leader_name: docContent.department_leader_name || newVal.department_leader_name || '',
      department_sign_year: docContent.department_sign_year || newVal.department_sign_year || '',
      department_sign_month: docContent.department_sign_month || newVal.department_sign_month || '',
      department_sign_day: docContent.department_sign_day || newVal.department_sign_day || '',
      supervisor_opinion: docContent.supervisor_opinion || newVal.supervisor_opinion || '',
      supervisor_name: docContent.supervisor_name || newVal.supervisor_name || '',
      supervisor_sign_year: docContent.supervisor_sign_year || newVal.supervisor_sign_year || '',
      supervisor_sign_month: docContent.supervisor_sign_month || newVal.supervisor_sign_month || '',
      supervisor_sign_day: docContent.supervisor_sign_day || newVal.supervisor_sign_day || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    tobacco_bureau_name: formData.value.tobacco_bureau_name,
    doc_year: formData.value.doc_year,
    doc_serial_number: formData.value.doc_serial_number,
    case_reason: formData.value.case_reason,
    case_source: formData.value.case_source,
    register_number: formData.value.register_number,
    case_date: formData.value.case_date,
    case_addr: formData.value.case_addr,
    company_name: formData.value.company_name,
    company_phone: formData.value.company_phone,
    legal_representative: formData.value.legal_representative,
    company_address: formData.value.company_address,
    person_name: formData.value.person_name,
    person_gender: formData.value.person_gender,
    person_age: formData.value.person_age,
    person_nationality: formData.value.person_nationality,
    id_type_and_number: formData.value.id_type_and_number,
    person_phone: formData.value.person_phone,
    person_address: formData.value.person_address,
    case_summary: formData.value.case_summary,
    undertaker_opinion: formData.value.undertaker_opinion,
    undertaker_name: formData.value.undertaker_name,
    undertaker_sign_year: formData.value.undertaker_sign_year,
    undertaker_sign_month: formData.value.undertaker_sign_month,
    undertaker_sign_day: formData.value.undertaker_sign_day,
    department_opinion: formData.value.department_opinion,
    department_leader_name: formData.value.department_leader_name,
    department_sign_year: formData.value.department_sign_year,
    department_sign_month: formData.value.department_sign_month,
    department_sign_day: formData.value.department_sign_day,
    supervisor_opinion: formData.value.supervisor_opinion,
    supervisor_name: formData.value.supervisor_name,
    supervisor_sign_year: formData.value.supervisor_sign_year,
    supervisor_sign_month: formData.value.supervisor_sign_month,
    supervisor_sign_day: formData.value.supervisor_sign_day
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '撤销立案报告表'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'caseInfo' || action === 'partyInfo' || action === 'opinion') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

.case-cancellation-report-container {
  padding: 20px;
  background: white;
  min-height: 100vh;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.header-left h3 {
  margin: 0;
  color: #495057;
  font-size: 18px;
  font-weight: 600;
}

.header-right {
  display: flex;
  gap: 10px;
}

.document-preview {
  margin-top: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.preview-content {
  min-height: 600px;
  background: white;
}

.document-body {
  margin-top: 20px;
}

.document-layout {
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-header-section {
  text-align: center;
  margin-bottom: 40px;
}

.org-name {
  margin-bottom: 15px;
}

.org-input {
  max-width: 300px;
  margin: 0 auto;
}

.document-title h2 {
  font-size: 24px;
  font-weight: bold;
  margin: 20px 0;
  color: #333;
}

.document-number {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-size: 16px;
  margin-top: 15px;
}

.content-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #fafafa;
}

.content-section h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.form-label {
  font-weight: 600;
  color: #495057;
  white-space: nowrap;
  min-width: fit-content;
}

.form-input {
  flex: 1;
  min-width: 150px;
}

.textarea-wrapper {
  margin-bottom: 20px;
}

.textarea-wrapper .form-label {
  display: block;
  margin-bottom: 10px;
}

.auto-resize-textarea {
  width: 100%;
}

.signature-section {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.signature-line {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

/* Element UI 样式调整 */
:deep(.el-input__wrapper) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  transition: border-color 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: #c0c4cc;
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

:deep(.el-textarea__inner) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  transition: border-color 0.2s;
  resize: vertical;
}

:deep(.el-textarea__inner:hover) {
  border-color: #c0c4cc;
}

:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

:deep(.el-select) {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .case-cancellation-report-container {
    padding: 10px;
  }

  .document-layout {
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .form-input {
    width: 100%;
  }

  .signature-line {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 打印样式 */
@media print {
  .document-header {
    display: none;
  }

  .case-cancellation-report-container {
    padding: 0;
    background: white;
  }

  .document-layout {
    box-shadow: none;
    border: none;
    max-width: none;
    padding: 20px;
  }

  .content-section {
    border: 1px solid #000;
    background: white;
  }
}
</style>
