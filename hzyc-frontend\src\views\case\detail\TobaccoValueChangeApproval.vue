<template>
  <div class="tobacco-price-change-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>烟草专卖品变价处理审批表</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">
          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="org-name">
              <el-input
                v-model="formData.org_short_name"
                placeholder="机构名称"
                class="org-input"
              />
            </div>

            <div class="document-title">
              <h2>违法物品变价处理审批表</h2>
            </div>
          </div>

          <!-- 变价处理信息表格 -->
          <div class="price-change-table-container">
            <table class="price-change-table">
              <tbody>
                <!-- 案由和立案编号 -->
                <tr>
                  <td class="label-cell">案由</td>
                  <td class="content-cell">
                    <el-input v-model="formData.case_name" size="small" placeholder="案由" />
                  </td>
                  <td class="label-cell">立案编号</td>
                  <td class="content-cell">
                    <el-input v-model="formData.case_code" size="small" placeholder="立案编号" />
                  </td>
                </tr>

                <!-- 申请变价处理原因 -->
                <tr>
                  <td class="label-cell">申请变价<br>处理原因</td>
                  <td class="content-cell" colspan="3">
                    <el-input
                      v-model="formData.price_change_reason"
                      type="textarea"
                      :rows="3"
                      size="small"
                      placeholder="申请变价处理原因" />
                  </td>
                </tr>

                <!-- 变价处理情况表格头部 -->
                <tr class="header-row">
                  <td :rowspan="formData.priceChangeList.length + 2" class="header-cell vertical-text">变<br>价<br>处<br>理<br>情<br>况</td>
                  <td class="header-cell">品种规格</td>
                  <td class="header-cell">数量（单位：<el-input v-model="formData.unit" size="small" style="width: 60px; display: inline-block;" placeholder="单位" />）</td>
                  <td class="header-cell">处理单价</td>
                  <td class="header-cell">处理金额</td>
                </tr>

                <!-- 变价处理数据行 -->
                <tr v-for="(item, index) in formData.priceChangeList" :key="index">
                  <td class="data-cell">
                    <el-input v-model="item.variety" size="small" placeholder="品种规格" />
                  </td>
                  <td class="data-cell">
                    <el-input v-model="item.quantity" size="small" placeholder="数量" />
                  </td>
                  <td class="data-cell">
                    <el-input v-model="item.handle_price" size="small" placeholder="处理单价" />
                  </td>
                  <td class="data-cell">
                    <el-input v-model="item.handle_amount" size="small" placeholder="处理金额" />
                  </td>
                </tr>

                <!-- 添加/删除物品按钮行 -->
                <tr>
                  <td colspan="4" style="text-align: center; padding: 10px;">
                    <el-button type="primary" size="small" @click="addPriceChangeRow">添加物品</el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="removePriceChangeRow(formData.priceChangeList.length - 1)"
                      :disabled="formData.priceChangeList.length <= 1"
                      style="margin-left: 10px;">
                      删除最后一行
                    </el-button>
                  </td>
                </tr>

                <!-- 承办部门意见 -->
                <tr>
                  <td class="label-cell">承办部门意见</td>
                  <td class="content-cell" colspan="4">
                    <el-input
                      v-model="formData.dept_opinion"
                      type="textarea"
                      :rows="2"
                      size="small"
                      placeholder="承办部门意见" />
                    <div style="margin-top: 10px; text-align: right;">
                      签名：<el-input v-model="formData.dept_handler" size="small" style="width: 100px; margin: 0 10px;" placeholder="签名" />
                      <el-input v-model="formData.dept_date_year" size="small" style="width: 60px;" placeholder="年" />年
                      <el-input v-model="formData.dept_date_month" size="small" style="width: 50px; margin: 0 5px;" placeholder="月" />月
                      <el-input v-model="formData.dept_date_day" size="small" style="width: 50px; margin: 0 5px;" placeholder="日" />日
                    </div>
                  </td>
                </tr>

                <!-- 定价小组意见 -->
                <tr>
                  <td class="label-cell">定价小组意见</td>
                  <td class="content-cell" colspan="4">
                    <el-input
                      v-model="formData.pricing_group_opinion"
                      type="textarea"
                      :rows="2"
                      size="small"
                      placeholder="定价小组意见" />
                    <div style="margin-top: 10px; text-align: right;">
                      签名：<el-input v-model="formData.pricing_group_handler" size="small" style="width: 100px; margin: 0 10px;" placeholder="签名" />
                      <el-input v-model="formData.pricing_date_year" size="small" style="width: 60px;" placeholder="年" />年
                      <el-input v-model="formData.pricing_date_month" size="small" style="width: 50px; margin: 0 5px;" placeholder="月" />月
                      <el-input v-model="formData.pricing_date_day" size="small" style="width: 50px; margin: 0 5px;" placeholder="日" />日
                    </div>
                  </td>
                </tr>

                <!-- 负责人审批 -->
                <tr>
                  <td class="label-cell">负责人审批</td>
                  <td class="content-cell" colspan="4">
                    <el-input
                      v-model="formData.leader_approval"
                      type="textarea"
                      :rows="2"
                      size="small"
                      placeholder="负责人审批" />
                    <div style="margin-top: 10px; text-align: right;">
                      签名：<el-input v-model="formData.leader_handler" size="small" style="width: 100px; margin: 0 10px;" placeholder="签名" />
                      <el-input v-model="formData.leader_date_year" size="small" style="width: 60px;" placeholder="年" />年
                      <el-input v-model="formData.leader_date_month" size="small" style="width: 50px; margin: 0 5px;" placeholder="月" />月
                      <el-input v-model="formData.leader_date_day" size="small" style="width: 50px; margin: 0 5px;" placeholder="日" />日
                    </div>
                  </td>
                </tr>

                <!-- 备注行 -->
                <tr class="remark-row">
                  <td class="label-cell">备注</td>
                  <td class="remark-content" colspan="4">
                    <el-input
                      v-model="formData.remark"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入备注信息"
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 经办人签名区域 -->
          <div class="signature-section">
            <div class="signature-row">
              <span class="signature-label">经办人：（签名）</span>
              <el-input v-model="formData.handler" size="small" style="width: 150px;" placeholder="经办人签名" />
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  org_short_name: '惠州市烟草专卖局',
  case_code: '',
  case_name: '',
  price_change_reason: '根据《广东省烟草专卖局涉案烟草制品价格管理规定》，对涉案违法物品进行价格重新评估和处理',
  unit: '条',
  priceChangeList: [
    {
      variety: '',
      quantity: '',
      handle_price: '',
      handle_amount: ''
    }
  ],
  dept_opinion: '',
  dept_handler: '',
  dept_date_year: '',
  dept_date_month: '',
  dept_date_day: '',
  pricing_group_opinion: '',
  pricing_group_handler: '',
  pricing_date_year: '',
  pricing_date_month: '',
  pricing_date_day: '',
  leader_approval: '',
  leader_handler: '',
  leader_date_year: '',
  leader_date_month: '',
  leader_date_day: '',
  remark: '',
  handler: '',
  // 保留的字段
  sys_modify_time: '',
  doc_date: null
})

// 预览相关状态
const previewLoading = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 处理变价处理列表数据
    let priceChangeList = []
    if (Array.isArray(docContent.priceChangeList) && docContent.priceChangeList.length > 0) {
      priceChangeList = docContent.priceChangeList
    } else if (Array.isArray(newVal.priceChangeList) && newVal.priceChangeList.length > 0) {
      priceChangeList = newVal.priceChangeList
    } else {
      // 保持默认的空物品行
      priceChangeList = formData.value.priceChangeList
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      org_short_name: docContent.org_short_name || newVal.org_short_name || formData.value.org_short_name,
      case_code: docContent.case_code || newVal.case_code || formData.value.case_code,
      case_name: docContent.case_name || newVal.case_name || formData.value.case_name,
      price_change_reason: docContent.price_change_reason || newVal.price_change_reason || formData.value.price_change_reason,
      unit: docContent.unit || newVal.unit || formData.value.unit,
      priceChangeList: priceChangeList,
      dept_opinion: docContent.dept_opinion || newVal.dept_opinion || formData.value.dept_opinion,
      dept_handler: docContent.dept_handler || newVal.dept_handler || formData.value.dept_handler,
      dept_date_year: docContent.dept_date_year || newVal.dept_date_year || formData.value.dept_date_year,
      dept_date_month: docContent.dept_date_month || newVal.dept_date_month || formData.value.dept_date_month,
      dept_date_day: docContent.dept_date_day || newVal.dept_date_day || formData.value.dept_date_day,
      pricing_group_opinion: docContent.pricing_group_opinion || newVal.pricing_group_opinion || formData.value.pricing_group_opinion,
      pricing_group_handler: docContent.pricing_group_handler || newVal.pricing_group_handler || formData.value.pricing_group_handler,
      pricing_date_year: docContent.pricing_date_year || newVal.pricing_date_year || formData.value.pricing_date_year,
      pricing_date_month: docContent.pricing_date_month || newVal.pricing_date_month || formData.value.pricing_date_month,
      pricing_date_day: docContent.pricing_date_day || newVal.pricing_date_day || formData.value.pricing_date_day,
      leader_approval: docContent.leader_approval || newVal.leader_approval || formData.value.leader_approval,
      leader_handler: docContent.leader_handler || newVal.leader_handler || formData.value.leader_handler,
      leader_date_year: docContent.leader_date_year || newVal.leader_date_year || formData.value.leader_date_year,
      leader_date_month: docContent.leader_date_month || newVal.leader_date_month || formData.value.leader_date_month,
      leader_date_day: docContent.leader_date_day || newVal.leader_date_day || formData.value.leader_date_day,
      remark: docContent.remark || newVal.remark || formData.value.remark,
      handler: docContent.handler || newVal.handler || formData.value.handler,
      doc_date: docContent.doc_date || newVal.doc_date || formData.value.doc_date,
      sys_modify_time: docContent.sys_modify_time || newVal.sys_modify_time || formData.value.sys_modify_time
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    org_short_name: formData.value.org_short_name,
    case_code: formData.value.case_code,
    case_name: formData.value.case_name,
    price_change_reason: formData.value.price_change_reason,
    unit: formData.value.unit,
    priceChangeList: formData.value.priceChangeList,
    dept_opinion: formData.value.dept_opinion,
    dept_handler: formData.value.dept_handler,
    dept_date_year: formData.value.dept_date_year,
    dept_date_month: formData.value.dept_date_month,
    dept_date_day: formData.value.dept_date_day,
    pricing_group_opinion: formData.value.pricing_group_opinion,
    pricing_group_handler: formData.value.pricing_group_handler,
    pricing_date_year: formData.value.pricing_date_year,
    pricing_date_month: formData.value.pricing_date_month,
    pricing_date_day: formData.value.pricing_date_day,
    leader_approval: formData.value.leader_approval,
    leader_handler: formData.value.leader_handler,
    leader_date_year: formData.value.leader_date_year,
    leader_date_month: formData.value.leader_date_month,
    leader_date_day: formData.value.leader_date_day,
    remark: formData.value.remark,
    handler: formData.value.handler,
    doc_date: formData.value.doc_date,
    sys_modify_time: formData.value.sys_modify_time || new Date().getTime()
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 新增变价处理行
const addPriceChangeRow = () => {
  formData.value.priceChangeList.push({
    variety: '',
    quantity: '',
    handle_price: '',
    handle_amount: ''
  })
}

// 删除变价处理行
const removePriceChangeRow = (index) => {
  if (formData.value.priceChangeList.length > 1) {
    formData.value.priceChangeList.splice(index, 1)
  }
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '烟草专卖品变价处理审批表'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'priceChangeInfo' || action === 'approvalInfo' || action === 'handlerInfo') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

/* 烟草专卖品变价处理审批表特有样式 */
.tobacco-price-change-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.price-change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.price-change-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

/* 变价处理信息表格样式 */
.price-change-table-container {
  margin: 30px 0;
}

.price-change-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #333;
}

.price-change-table td {
  border: 1px solid #333;
  padding: 8px;
  vertical-align: middle;
  min-height: 40px;
}

.label-cell {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center;
  width: 100px;
  vertical-align: middle;
}

.content-cell {
  padding: 8px 12px;
  text-align: left;
}

.header-row {
  background-color: #f0f0f0;
}

.header-cell {
  background-color: #f0f0f0;
  font-weight: bold;
  text-align: center;
  padding: 12px 8px;
  vertical-align: middle;
}

.data-cell {
  text-align: center;
  padding: 8px;
}

.vertical-text {
  writing-mode: vertical-lr;
  text-orientation: upright;
  width: 60px;
  text-align: center;
  vertical-align: middle;
  font-weight: bold;
  background-color: #f5f5f5;
}

.remark-row {
  height: 100px;
}

.remark-content {
  text-align: left;
  vertical-align: top;
  padding: 10px;
}

.signature-section {
  margin-top: 50px;
}

.signature-row {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  line-height: 2;
}

.signature-label {
  font-weight: 500;
  margin-right: 10px;
  white-space: nowrap;
}

/* 确保输入框在表格中的样式 */
.price-change-table .el-input {
  width: 100%;
}

.price-change-table .el-input__wrapper {
  border: none;
  box-shadow: none;
  background: transparent;
}

.price-change-table .el-input__inner {
  text-align: center;
}

.price-change-table .el-textarea__inner {
  border: none;
  box-shadow: none;
  background: transparent;
  resize: none;
}

/* 审批意见区域样式 */
.approval-section {
  margin: 20px 0;
}

.approval-row {
  margin-bottom: 30px;
}

.approval-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.signature-area {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

/* 打印样式 */
@media print {
  .document-header {
    display: none;
  }

  .price-change-header .el-button {
    display: none;
  }

  .price-change-table th:last-child,
  .price-change-table td:last-child {
    display: none;
  }

  .document-layout {
    padding: 0;
    box-shadow: none;
  }
}
</style>