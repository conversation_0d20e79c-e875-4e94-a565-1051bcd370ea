<template>
  <div class="main-container">
    <div class="main-wrapper">
      <!-- 左侧面板 -->
      <div class="left-panel">
      <!-- 案件信息表单 -->
        <el-card class="section-card" style="height: 341px;" >
          <template #header>
            <div class="section-title">
              <el-icon><edit /></el-icon>
              填写案件信息
            </div>
          </template>

          <el-form :model="caseInfo" label-width="80px" label-position="top" class="case-form">
            <el-form-item label="案件名称">
              <el-select
                v-model="caseInfo.name"
                placeholder="请选择或输入案件名称"
                filterable
                allow-create
                clearable
                remote
                :remote-method="getCaseData"
                @change="onCaseNameChange"
                @clear="resetForm"
                style="width: 100%"
              >
                <el-option
                  v-for="caseItem in caseData"
                  :key="caseItem.caseName"
                  :label="caseItem.caseName"
                  :value="caseItem.caseName"
                />
              </el-select>
            </el-form-item>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="当事人信息">
                  <el-input
                    v-model="caseInfo.party"
                    placeholder="请输入当事人姓名、身份证号等信息"
                    :disabled="isFormDisabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="案件来源">
                  <el-select
                    v-model="caseInfo.caseSource"
                    placeholder="请选择案件来源"
                    :disabled="isFormDisabled"
                    style="width: 100%"
                  >
                    <el-option label="投诉举报" value="01" />
                    <el-option label="市场查获" value="02" />
                    <el-option label="案件移交" value="03" />
                    <el-option label="指定管辖" value="04" />
                    <el-option label="其他" value="06" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="案件描述">
              <el-input
                v-model="caseInfo.description"
                type="textarea"
                :rows="3"
                placeholder="请详细描述案件情况、违法事实等"
                :disabled="isFormDisabled"
              />
            </el-form-item>
          </el-form>
        </el-card>
        <!-- 生成模式选择 -->
        <el-card class="section-card" >
          <template #header>
            <div class="section-title">
              <el-icon><setting /></el-icon>
              生成模式
            </div>
          </template>

          <el-radio-group
            v-model="generationMode"
            class="mode-selector"
            :disabled="!caseInfo.name || !caseInfo.id"
            @change="onGenerationModeChange"
          >
            <el-radio value="single" class="mode-option">
              <el-card class="mode-content single-mode" :class="{ 
                active: generationMode === 'single',
                disabled: !caseInfo.name || !caseInfo.id
              }">
                <div class="mode-check" v-if="generationMode === 'single'">
                  <el-icon><Select /></el-icon>
                </div>
                <el-icon style="color: #409eff;"><document /></el-icon>
                <span>单独生成</span>
                <p>选择特定文档类型生成</p>
              </el-card>
            </el-radio>
            <el-radio value="batch" class="mode-option" :disabled="!hasAvailableDocuments">
              <el-card class="mode-content batch-mode" :class="{
                active: generationMode === 'batch',
                disabled: !caseInfo.name || !caseInfo.id || !hasAvailableDocuments
              }">
                <div class="mode-check" v-if="generationMode === 'batch'">
                  <el-icon><Check /></el-icon>
                </div>
                <el-icon style="color: #67c23a;"><files /></el-icon>
                <span>批量生成</span>
                <p v-if="hasAvailableDocuments">一次性生成所有案卷文件</p>
                <p v-else style="color: #e6a23c;">暂无可用文档，敬请期待</p>
              </el-card>
            </el-radio>
          </el-radio-group>
        </el-card>
        <!-- 文档类型选择 -->
        <el-card v-if="generationMode === 'single'" class="section-card" style="height: 330px;">
          <template #header>
            <div class="section-title">
              <el-icon><document /></el-icon>
              将生成以下文件
            </div>
          </template>

          <!-- 搜索框 -->
          <div class="search-container">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文档类型..."
              clearable
              prefix-icon="Search"
              @input="filterDocumentTypes"
              :disabled="!caseInfo.name || !caseInfo.id"
            />
          </div>

          <!-- 文档类型列表 -->
          <div class="document-list">
            <el-card
              v-for="docType in filteredDocumentTypes"
              :key="docType.value"
              class="doc-list-item"
              :class="{
                active: selectedDocType === docType.value,
                generated: isDocumentGenerated(docType.value),
                disabled: !caseInfo.name || !caseInfo.id,
                developing: docType.status === 'developing'
              }"
              @click="handleDocTypeClick(docType.value)"
              shadow="hover"
            >
              <div class="doc-list-content">
                <div class="doc-status">
                  <el-icon v-if="selectedDocType !== docType.value">
                    <component :is="docType.icon" />
                  </el-icon>
                  <el-icon v-else class="selected-icon">
                    <circle-check />
                  </el-icon>
                </div>
                <div class="doc-info">
                  <div class="doc-name" :class="{ selected: selectedDocType === docType.value }">
                    {{ docType.label }}
                    <el-tag v-if="isDocumentGenerated(docType.value)" type="success" size="small" class="generated-tag">
                      已生成
                    </el-tag>
                    <el-tag v-if="docType.status === 'developing'" type="warning" size="small" class="developing-tag">
                      开发中，敬请期待
                    </el-tag>
                  </div>
                  <div class="doc-description">{{ docType.description }}</div>
                </div>
                <div class="doc-check" v-if="selectedDocType === docType.value">
                  <el-icon><Check /></el-icon>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 无搜索结果提示 -->
          <div v-if="filteredDocumentTypes.length === 0" class="no-results">
            <el-icon><document /></el-icon>
            <p>未找到匹配的文档类型</p>
          </div>
        </el-card>

        <!-- 批量生成文件列表 -->
        <el-card v-if="generationMode === 'batch'" class="section-card" style="height: 330px;">
          <template #header>
            <div class="section-title">
              <el-icon><files /></el-icon>
              批量生成文件
              <div style="float: right; font-size: 12px; color: #666;">
                已完成: {{ batchFiles.filter(f => f.developStatus === 'completed').length }} /
                开发中: {{ batchFiles.filter(f => f.developStatus === 'developing').length }}
              </div>
            </div>
          </template>

          <div class="batch-files" style="max-height: 280px; overflow-y: auto;">
            <div
              v-for="file in batchFiles"
              :key="file.id"
              class="doc-type-item"
              :class="{
                active: selectedBatchFiles.includes(file.value),
                generated: isDocumentGenerated(file.value),
                disabled: !caseInfo.name || !caseInfo.id,
                developing: file.developStatus === 'developing'
              }"
              @click="handleBatchFileClick(file.value)"
            >
              <div class="doc-type-content">
                <div class="doc-type-check" v-if="selectedBatchFiles.includes(file.value)">
                  <el-icon><Check /></el-icon>
                </div>
                <el-icon><component :is="file.icon || 'Document'" /></el-icon>
                <span>
                  {{ file.name }}
                  <el-tag v-if="isDocumentGenerated(file.value)" type="success" size="small" class="generated-tag-batch">
                    已生成
                  </el-tag>
                  <el-tag v-if="file.developStatus === 'developing'" type="warning" size="small" class="developing-tag-batch">
                    开发中，敬请期待
                  </el-tag>
                </span>
                <p>{{ file.type }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <!-- 操作按钮区域 - 新增 -->
        <el-card class="section-card" style="height: 148px;">
          <template #header>
            <div class="section-title">
              <el-icon><cpu /></el-icon>
              智能生成
            </div>
          </template>
          
          <div class="generate-actions">
            <el-button
              class="generate-btn-top"
              @click="handleGenerateDocument"
              :loading="generating"
              :disabled="isGenerateDisabled"
              type="primary"
            >
              <template #icon>
                <el-icon v-if="!generating">
                  <cpu />
                </el-icon>
              </template>
              <span v-if="!generating">
                <span v-if="generationMode === 'single'">
                  {{ hasGenerated ? '重新生成文书' : '智能生成文书' }}
                </span>
                <span v-else>
                  {{ hasGenerated ? '重新生成' : '批量生成所有文件' }}
                </span>
              </span>
              <span v-else>
                <span v-if="generationMode === 'single'">正在生成中...</span>
                <span v-else>正在批量生成中...</span>
              </span>
            </el-button>
            
            <el-button
              v-if="hasGenerated"
              class="preview-btn"
              @click="previewFiles"
              type="success"
              plain
            >
              <template #icon>
                <el-icon><View /></el-icon>
              </template>
              查看文件详情
            </el-button>
          </div>
        </el-card>

        <el-card class="section-card" style="height: 671px;">
        <template #header>
          <div class="section-title">
            <el-icon><loading /></el-icon>
            生成进度
          </div>
        </template>

        <div class="progress-container">
          <el-steps :active="currentStep - 1" direction="vertical" finish-status="success">
            <el-step title="信息收集" description="分析案件信息和文档类型" />
            <el-step title="AI智能分析" description="大模型理解案件内容要素" />
            <el-step title="文书生成" description="按照标准格式生成文书" />
            <el-step title="质量检查" description="检查格式和内容完整性" />
          </el-steps>
        </div>

        <div class="tips-section-merged">
          <div class="tips-header">
            <el-icon><warning /></el-icon>
            使用提示
          </div>
          <ul class="tips-list">
            <li>请确保案件信息填写完整准确</li>
            <li>选择正确的文档类型以获得最佳效果</li>
            <li>生成后可进一步编辑和完善内容</li>
            <li>系统会自动保存生成记录</li>
            <li style="color: #e6a23c;">标记为"开发中"的文档暂不可选择</li>
            <li style="color: #67c23a;">已完成开发的文档可正常使用</li>
          </ul>
        </div>
      </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Document,
  Files,
  Setting,
  Loading,
  CircleCheck,
  CircleCheckFilled,
  WarningFilled,
  Check,
  Select,
  View,
  Cpu
} from '@element-plus/icons-vue';
import { getList } from '@/api/cases/cases';
import { generateDocument } from '@/api/cases/documents';
import { getList as getDocumentList } from '@/api/document/document';

const router = useRouter();
const route = useRoute(); // 在 setup 顶层声明

// 响应式数据
const generationMode = ref('single');
const selectedDocType = ref('');
const generating = ref(false);
const hasGenerated = ref(false);
const currentStep = ref(1);
const isFormDisabled = ref(false);
const searchKeyword = ref('');
const filteredDocumentTypes = ref([]);

// 已生成的文件列表
const generatedFiles = ref([]);

// 批量生成选中的文件
const selectedBatchFiles = ref([]);

// 文档类型配置 - 更新为完整的65种文档类型，添加开发状态
const documentTypes = ref([
  { value: 'report-record', label: '举报记录表', icon: 'Document', description: '记录举报相关信息', status: 'developing' },
  { value: 'case-filing-report', label: '立案报告表', icon: 'Document', description: '案件立案相关报告', status: 'developing' },
  { value: 'non-filing-report', label: '不予立案报告表', icon: 'Document', description: '不予立案情况报告', status: 'developing' },
  { value: 'non-filing-notice', label: '不予立案告知书', icon: 'Bell', description: '不予立案告知文书', status: 'developing' },
  { value: 'extend-filing-approval', label: '延长立案期限审批表', icon: 'Clock', description: '延长立案期限审批', status: 'developing' },
  { value: 'extend-filing-notice', label: '延长立案期限告知书', icon: 'Bell', description: '延长立案期限告知', status: 'developing' },
  { value: 'jurisdiction-notice', label: '指定管辖通知书', icon: 'Bell', description: '指定管辖相关通知', status: 'developing' },
  { value: 'scene-record', label: '现场笔录', icon: 'View', description: '现场检查情况记录', status: 'developing' },
  { value: 'electronic-data-record', label: '电子数据证据提取笔录', icon: 'Monitor', description: '电子数据证据提取记录', status: 'developing' },
  { value: 'evidence-preservation-approval', label: '证据先行登记保存批准书', icon: 'Lock', description: '证据保存批准文书', status: 'developing' },
  { value: 'evidence-preservation-notice', label: '证据先行登记保存通知书', icon: 'Bell', description: '证据保存通知文书', status: 'developing' },
  { value: 'evidence-handling-notice', label: '先行登记保存证据处理通知书', icon: 'Bell', description: '证据处理通知文书', status: 'developing' },
  { value: 'sampling-list', label: '抽样取证物品清单', icon: 'List', description: '抽样取证物品详细清单', status: 'completed' },
  { value: 'inspection-sample-notice', label: '鉴别检验留样告知书', icon: 'Bell', description: '鉴别检验留样告知', status: 'developing' },
  { value: 'inquiry-notice', label: '询问通知书', icon: 'Bell', description: '询问相关通知', status: 'completed' },
  { value: 'inquiry-record', label: '询问笔录', icon: 'ChatLineRound', description: '记录询问过程和内容', status: 'completed' },
  { value: 'evidence-copy-form', label: '证据复制（提取）单', icon: 'CopyDocument', description: '证据复制提取单据', status: 'completed' },
  { value: 'item-valuation-form', label: '涉案物品核价表', icon: 'Money', description: '涉案物品价值核定', status: 'developing' },
  { value: 'announcement', label: '公告', icon: 'Promotion', description: '相关公告文书', status: 'developing' },
  { value: 'case-transfer-letter', label: '案件移送函', icon: 'Switch', description: '案件移送相关函件', status: 'developing' },
  { value: 'property-transfer-list', label: '移送财物清单', icon: 'List', description: '移送财物详细清单', status: 'developing' },
  { value: 'assistance-letter', label: '协助函', icon: 'Connection', description: '协助相关函件', status: 'developing' },
  { value: 'investigation-report', label: '案件调查终结报告', icon: 'Document', description: '案件调查终结报告', status: 'completed' },
  { value: 'item-return-list', label: '涉案物品返还清单', icon: 'List', description: '涉案物品返还清单', status: 'developing' },
  { value: 'sample-return-list', label: '鉴别检验留样返还清单', icon: 'List', description: '留样返还清单', status: 'developing' },
  { value: 'case-review-form', label: '案件处理初审表', icon: 'Document', description: '案件处理初审表', status: 'developing' },
  { value: 'penalty-notice', label: '行政处罚事先告知书', icon: 'Warning', description: '行政处罚事先告知', status: 'completed' },
  { value: 'statement-record', label: '陈述申辩记录', icon: 'ChatLineRound', description: '陈述申辩记录', status: 'developing' },
  { value: 'statement-review-form', label: '陈述申辩意见复核表', icon: 'Document', description: '陈述申辩意见复核', status: 'developing' },
  { value: 'hearing-notice', label: '听证告知书', icon: 'Bell', description: '听证告知文书', status: 'completed' },
  { value: 'hearing-notification', label: '听证通知书', icon: 'Bell', description: '听证通知文书', status: 'developing' },
  { value: 'hearing-rejection-notice', label: '不予受理听证通知书', icon: 'Bell', description: '不予受理听证通知', status: 'developing' },
  { value: 'hearing-announcement', label: '听证公告', icon: 'Promotion', description: '听证公告文书', status: 'developing' },
  { value: 'hearing-record', label: '听证笔录', icon: 'Microphone', description: '听证会过程记录', status: 'developing' },
  { value: 'hearing-report', label: '听证报告', icon: 'Document', description: '听证报告文书', status: 'developing' },
  { value: 'case-approval-form', label: '案件处理审批表', icon: 'Document', description: '案件处理审批表', status: 'completed' },
  { value: 'case-discussion-record', label: '案件集体讨论记录', icon: 'ChatLineRound', description: '案件集体讨论记录', status: 'completed' },
  { value: 'extend-decision-approval', label: '延长作出行政处罚决定期限审批表', icon: 'Clock', description: '延长处罚决定期限审批', status: 'developing' },
  { value: 'extend-decision-notice', label: '延长作出行政处罚决定期限告知书', icon: 'Bell', description: '延长处罚决定期限告知', status: 'developing' },
  { value: 'immediate-penalty-decision', label: '当场行政处罚决定书', icon: 'Warning', description: '当场行政处罚决定', status: 'developing' },
  { value: 'penalty-decision', label: '行政处罚决定书', icon: 'Warning', description: '行政处罚决定书', status: 'completed' },
  { value: 'administrative-decision', label: '行政处理决定书', icon: 'Document', description: '行政处理决定书', status: 'completed' },
  { value: 'no-penalty-decision', label: '不予行政处罚决定书', icon: 'Document', description: '不予行政处罚决定', status: 'developing' },
  { value: 'electronic-delivery-confirmation', label: '电子送达方式确认书', icon: 'Message', description: '电子送达方式确认', status: 'developing' },
  { value: 'delivery-receipt', label: '送达回证', icon: 'Document', description: '送达回证文书', status: 'completed' },
  { value: 'delivery-announcement', label: '送达公告', icon: 'Promotion', description: '送达公告文书', status: 'completed' },
  { value: 'correction-notice', label: '责令改正通知书', icon: 'Bell', description: '责令改正通知', status: 'completed' },
  { value: 'review-record', label: '复查记录表', icon: 'Document', description: '复查记录表', status: 'developing' },
  { value: 'rectification-notice', label: '整顿终结通知书', icon: 'Bell', description: '整顿终结通知', status: 'developing' },
  { value: 'destruction-record', label: '销毁记录表', icon: 'Delete', description: '销毁记录表', status: 'developing' },
  { value: 'tobacco-value-change-approval', label: '烟草专卖品变价处理审批表', icon: 'Money', description: '烟草专卖品变价处理审批', status: 'completed' },
  { value: 'tobacco-transfer-form', label: '烟草专卖品移交单', icon: 'Switch', description: '烟草专卖品移交单', status: 'developing' },
  { value: 'enforcement-approval', label: '行政强制执行事项审批表', icon: 'Document', description: '行政强制执行事项审批', status: 'developing' },
  { value: 'additional-fine-decision', label: '加处罚款决定书', icon: 'Money', description: '加处罚款决定书', status: 'developing' },
  { value: 'compliance-notice', label: '行政处罚决定履行催告书', icon: 'Bell', description: '行政处罚决定履行催告', status: 'developing' },
  { value: 'enforcement-application', label: '行政处罚强制执行申请书', icon: 'Document', description: '行政处罚强制执行申请', status: 'developing' },
  { value: 'fine-payment-extension-approval', label: '延期（分期）缴纳罚款审批表', icon: 'Clock', description: '延期分期缴纳罚款审批', status: 'developing' },
  { value: 'fine-payment-extension-notice', label: '延期（分期）缴纳罚款通知书', icon: 'Bell', description: '延期分期缴纳罚款通知', status: 'developing' },
  { value: 'award-report-form', label: '对协助办案有功个人、单位授奖呈报表', icon: 'Trophy', description: '协助办案授奖呈报', status: 'developing' },
  { value: 'case-withdrawal-report', label: '撤销立案报告表', icon: 'Document', description: '撤销立案报告', status: 'completed' },
  { value: 'case-withdrawal-notice', label: '撤销立案通知书', icon: 'Bell', description: '撤销立案通知', status: 'developing' },
  { value: 'case-closure-report', label: '结案报告表', icon: 'Document', description: '结案报告表', status: 'developing' },
  { value: 'case-cover', label: '卷宗封面', icon: 'Folder', description: '卷宗封面', status: 'developing' },
  { value: 'case-directory', label: '卷宗目录', icon: 'Menu', description: '卷宗目录', status: 'developing' },
  { value: 'case-reference-form', label: '卷内备考表', icon: 'Document', description: '卷内备考表', status: 'developing' }
]);

// 案件信息
const caseInfo = reactive({
  id:'',
  name: '',
  party: '',
  caseType: '',
  description: '',
});

// 批量文件列表 - 更新为完整的65种文档类型，设置默认勾选，添加开发状态
const batchFiles = ref([
  { id: 1, name: '举报记录表', type: '记录类', status: 'pending', icon: 'Document', checked: true, value: 'report-record', developStatus: 'developing' },
  { id: 2, name: '立案报告表', type: '报告类', status: 'pending', icon: 'Document', checked: true, value: 'case-filing-report', developStatus: 'developing' },
  { id: 3, name: '不予立案报告表', type: '报告类', status: 'pending', icon: 'Document', checked: false, value: 'non-filing-report', developStatus: 'developing' },
  { id: 4, name: '不予立案告知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'non-filing-notice', developStatus: 'developing' },
  { id: 5, name: '延长立案期限审批表', type: '审批类', status: 'pending', icon: 'Clock', checked: false, value: 'extend-filing-approval', developStatus: 'developing' },
  { id: 6, name: '延长立案期限告知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'extend-filing-notice', developStatus: 'developing' },
  { id: 7, name: '指定管辖通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'jurisdiction-notice', developStatus: 'developing' },
  { id: 8, name: '现场笔录', type: '笔录类', status: 'pending', icon: 'View', checked: true, value: 'scene-record', developStatus: 'developing' },
  { id: 9, name: '电子数据证据提取笔录', type: '笔录类', status: 'pending', icon: 'Monitor', checked: false, value: 'electronic-data-record', developStatus: 'developing' },
  { id: 10, name: '证据先行登记保存批准书', type: '批准类', status: 'pending', icon: 'Lock', checked: true, value: 'evidence-preservation-approval', developStatus: 'developing' },
  { id: 11, name: '证据先行登记保存通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: true, value: 'evidence-preservation-notice', developStatus: 'developing' },
  { id: 12, name: '先行登记保存证据处理通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: true, value: 'evidence-handling-notice', developStatus: 'developing' },
  { id: 13, name: '抽样取证物品清单', type: '清单类', status: 'pending', icon: 'List', checked: true, value: 'sampling-list', developStatus: 'completed' },
  { id: 14, name: '鉴别检验留样告知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'inspection-sample-notice', developStatus: 'developing' },
  { id: 15, name: '询问通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'inquiry-notice', developStatus: 'completed' },
  { id: 16, name: '询问笔录', type: '笔录类', status: 'pending', icon: 'ChatLineRound', checked: true, value: 'inquiry-record', developStatus: 'completed' },
  { id: 17, name: '证据复制（提取）单', type: '单据类', status: 'pending', icon: 'CopyDocument', checked: true, value: 'evidence-copy-form', developStatus: 'completed' },
  { id: 18, name: '涉案物品核价表', type: '表格类', status: 'pending', icon: 'Money', checked: false, value: 'item-valuation-form', developStatus: 'developing' },
  { id: 19, name: '公告', type: '公告类', status: 'pending', icon: 'Promotion', checked: true, value: 'announcement', developStatus: 'developing' },
  { id: 20, name: '案件移送函', type: '函件类', status: 'pending', icon: 'Switch', checked: false, value: 'case-transfer-letter', developStatus: 'developing' },
  { id: 21, name: '移送财物清单', type: '清单类', status: 'pending', icon: 'List', checked: false, value: 'property-transfer-list', developStatus: 'developing' },
  { id: 22, name: '协助函', type: '函件类', status: 'pending', icon: 'Connection', checked: false, value: 'assistance-letter', developStatus: 'developing' },
  { id: 23, name: '案件调查终结报告', type: '报告类', status: 'pending', icon: 'Document', checked: true, value: 'investigation-report', developStatus: 'completed' },
  { id: 24, name: '涉案物品返还清单', type: '清单类', status: 'pending', icon: 'List', checked: false, value: 'item-return-list', developStatus: 'developing' },
  { id: 25, name: '鉴别检验留样返还清单', type: '清单类', status: 'pending', icon: 'List', checked: false, value: 'sample-return-list', developStatus: 'developing' },
  { id: 26, name: '案件处理初审表', type: '审查类', status: 'pending', icon: 'Document', checked: true, value: 'case-review-form', developStatus: 'developing' },
  { id: 27, name: '行政处罚事先告知书', type: '通知类', status: 'pending', icon: 'Warning', checked: true, value: 'penalty-notice', developStatus: 'completed' },
  { id: 28, name: '陈述申辩记录', type: '记录类', status: 'pending', icon: 'ChatLineRound', checked: false, value: 'statement-record', developStatus: 'developing' },
  { id: 29, name: '陈述申辩意见复核表', type: '复核类', status: 'pending', icon: 'Document', checked: false, value: 'statement-review-form', developStatus: 'developing' },
  { id: 30, name: '听证告知书', type: '通知类', status: 'pending', icon: 'Bell', checked: true, value: 'hearing-notice', developStatus: 'completed' },
  { id: 31, name: '听证通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'hearing-notification', developStatus: 'developing' },
  { id: 32, name: '不予受理听证通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'hearing-rejection-notice', developStatus: 'developing' },
  { id: 33, name: '听证公告', type: '公告类', status: 'pending', icon: 'Promotion', checked: false, value: 'hearing-announcement', developStatus: 'developing' },
  { id: 34, name: '听证笔录', type: '笔录类', status: 'pending', icon: 'Microphone', checked: false, value: 'hearing-record', developStatus: 'developing' },
  { id: 35, name: '听证报告', type: '报告类', status: 'pending', icon: 'Document', checked: false, value: 'hearing-report', developStatus: 'developing' },
  { id: 36, name: '案件处理审批表', type: '审批类', status: 'pending', icon: 'Document', checked: true, value: 'case-approval-form', developStatus: 'completed' },
  { id: 37, name: '案件集体讨论记录', type: '记录类', status: 'pending', icon: 'ChatLineRound', checked: true, value: 'case-discussion-record', developStatus: 'completed' },
  { id: 38, name: '延长作出行政处罚决定期限审批表', type: '审批类', status: 'pending', icon: 'Clock', checked: false, value: 'extend-decision-approval', developStatus: 'developing' },
  { id: 39, name: '延长作出行政处罚决定期限告知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'extend-decision-notice', developStatus: 'developing' },
  { id: 40, name: '当场行政处罚决定书', type: '决定类', status: 'pending', icon: 'Warning', checked: false, value: 'immediate-penalty-decision', developStatus: 'developing' },
  { id: 41, name: '行政处罚决定书', type: '决定类', status: 'pending', icon: 'Warning', checked: true, value: 'penalty-decision', developStatus: 'completed' },
  { id: 42, name: '行政处理决定书', type: '决定类', status: 'pending', icon: 'Document', checked: true, value: 'administrative-decision', developStatus: 'completed' },
  { id: 43, name: '不予行政处罚决定书', type: '决定类', status: 'pending', icon: 'Document', checked: false, value: 'no-penalty-decision', developStatus: 'developing' },
  { id: 44, name: '电子送达方式确认书', type: '确认类', status: 'pending', icon: 'Message', checked: false, value: 'electronic-delivery-confirmation', developStatus: 'developing' },
  { id: 45, name: '送达回证', type: '回证类', status: 'pending', icon: 'Document', checked: true, value: 'delivery-receipt', developStatus: 'completed' },
  { id: 46, name: '送达公告', type: '公告类', status: 'pending', icon: 'Promotion', checked: false, value: 'delivery-announcement', developStatus: 'completed' },
  { id: 47, name: '责令改正通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'correction-notice', developStatus: 'completed' },
  { id: 48, name: '复查记录表', type: '记录类', status: 'pending', icon: 'Document', checked: false, value: 'review-record', developStatus: 'developing' },
  { id: 49, name: '整顿终结通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'rectification-notice', developStatus: 'developing' },
  { id: 50, name: '销毁记录表', type: '记录类', status: 'pending', icon: 'Delete', checked: false, value: 'destruction-record', developStatus: 'developing' },
  { id: 51, name: '烟草专卖品变价处理审批表', type: '审批类', status: 'pending', icon: 'Money', checked: false, value: 'tobacco-value-change-approval', developStatus: 'completed' },
  { id: 52, name: '烟草专卖品移交单', type: '单据类', status: 'pending', icon: 'Switch', checked: false, value: 'tobacco-transfer-form', developStatus: 'developing' },
  { id: 53, name: '行政强制执行事项审批表', type: '审批类', status: 'pending', icon: 'Document', checked: false, value: 'enforcement-approval', developStatus: 'developing' },
  { id: 54, name: '加处罚款决定书', type: '决定类', status: 'pending', icon: 'Money', checked: false, value: 'additional-fine-decision', developStatus: 'developing' },
  { id: 55, name: '行政处罚决定履行催告书', type: '催告类', status: 'pending', icon: 'Bell', checked: false, value: 'compliance-notice', developStatus: 'developing' },
  { id: 56, name: '行政处罚强制执行申请书', type: '申请类', status: 'pending', icon: 'Document', checked: false, value: 'enforcement-application', developStatus: 'developing' },
  { id: 57, name: '延期（分期）缴纳罚款审批表', type: '审批类', status: 'pending', icon: 'Clock', checked: false, value: 'fine-payment-extension-approval', developStatus: 'developing' },
  { id: 58, name: '延期（分期）缴纳罚款通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'fine-payment-extension-notice', developStatus: 'developing' },
  { id: 59, name: '对协助办案有功个人、单位授奖呈报表', type: '呈报类', status: 'pending', icon: 'Trophy', checked: false, value: 'award-report-form', developStatus: 'developing' },
  { id: 60, name: '撤销立案报告表', type: '报告类', status: 'pending', icon: 'Document', checked: false, value: 'case-withdrawal-report', developStatus: 'completed' },
  { id: 61, name: '撤销立案通知书', type: '通知类', status: 'pending', icon: 'Bell', checked: false, value: 'case-withdrawal-notice', developStatus: 'developing' },
  { id: 62, name: '结案报告表', type: '报告类', status: 'pending', icon: 'Document', checked: true, value: 'case-closure-report', developStatus: 'developing' },
  { id: 63, name: '卷宗封面', type: '封面类', status: 'pending', icon: 'Folder', checked: true, value: 'case-cover', developStatus: 'developing' },
  { id: 64, name: '卷宗目录', type: '目录类', status: 'pending', icon: 'Menu', checked: true, value: 'case-directory', developStatus: 'developing' },
  { id: 65, name: '卷内备考表', type: '表格类', status: 'pending', icon: 'Document', checked: false, value: 'case-reference-form', developStatus: 'developing' }
]);

const onCaseNameChange = async (selectedName) => {
  const selectedCase = caseData.value.find(caseItem => caseItem.caseName === selectedName);

  if (selectedCase) {
    caseInfo.party = selectedCase.party;
    caseInfo.id=selectedCase.id;
    caseInfo.caseType = selectedCase.caseType;
    caseInfo.description = selectedCase.caseBrief;
    caseInfo.caseSource = selectedCase.caseSource;
    isFormDisabled.value = true;

    // 获取案件已生成的文件
    await fetchGeneratedFiles(selectedCase.id || selectedCase.caseId);
  } else {
    caseInfo.party = '';
    caseInfo.caseType = '';
    caseInfo.description = '';
    isFormDisabled.value = false;

    // 清空已生成文件列表
    generatedFiles.value = [];

    if (selectedName && selectedName.trim()) {
      ElMessage.info('请手动填写案件信息');
    }
  }
};

const resetForm = () => {
  caseInfo.name = '';
  caseInfo.party = '';
  caseInfo.caseType = '';
  caseInfo.description = '';
  isFormDisabled.value = false;
};
// 预览文件详情
const previewFiles = () => {
  if (generationMode.value === 'single') {
    if (!selectedDocType.value) {
      ElMessage.warning('请先选择要预览的文档类型');
      return;
    }
    
    // 跳转到单个文件详情
    const selectedDoc = documentTypes.value.find(doc => doc.value === selectedDocType.value);
    router.push({
      name: '案卷文书详情',
      params: {
        fileId: selectedDoc?.value || 'default'
      },
      query: {
        fileName: selectedDoc?.label || '未知文件',
        fileType: selectedDoc?.description || '文档类型',
        caseId: caseInfo.id || 'default',
        mode: 'preview',
        source: 'case-generate' // 添加来源参数，区分案卷评查和案卷生成
      }
    });
  } else {
    // 批量模式 - 跳转到文件列表页面
    if (selectedBatchFiles.value.length === 0) {
      ElMessage.warning('请先选择要预览的文件');
      return;
    }
    
    router.push({
      name: '案卷文书详情',
      params: {
        fileId: 'batch'
      },
      query: {
        fileName: '批量文件预览',
        fileType: '批量模式',
        caseId: caseInfo.name || 'default',
        selectedFiles: selectedBatchFiles.value.join(','),
        mode: 'batch-preview',
        source: 'case-generate' // 添加来源参数，区分案卷评查和案卷生成
      }
    });
  }
};
const handleGenerateDocument = async () => {
  if (generationMode.value === 'single' && !selectedDocType.value) {
    ElMessage.warning('请选择要生成的文档类型');
    return;
  }

  if (!caseInfo.name || !caseInfo.party) {
    ElMessage.warning('请填写完整的案件信息');
    return;
  }

  // 获取案件ID
  const selectedCase = caseData.value.find(caseItem => caseItem.caseName === caseInfo.name);
  const caseId = selectedCase?.id || selectedCase?.caseId;

  if (!caseId) {
    ElMessage.warning('未找到案件ID，请重新选择案件');
    return;
  }

  // 检查要生成的文档是否包含开发中的类型
  const filesToGenerate = generationMode.value === 'single'
    ? [selectedDocType.value]
    : selectedBatchFiles.value;

  // 过滤掉开发中的文档
  const validFilesToGenerate = filesToGenerate.filter(fileType => {
    if (generationMode.value === 'single') {
      const docType = documentTypes.value.find(doc => doc.value === fileType);
      return docType?.status === 'completed';
    } else {
      const file = batchFiles.value.find(f => f.value === fileType);
      return file?.developStatus === 'completed';
    }
  });

  if (validFilesToGenerate.length === 0) {
    ElMessage.warning('所选文档类型均在开发中，请选择其他文档类型');
    return;
  }

  if (validFilesToGenerate.length < filesToGenerate.length) {
    const developingCount = filesToGenerate.length - validFilesToGenerate.length;
    ElMessage.info(`已过滤掉${developingCount}个开发中的文档类型，将生成${validFilesToGenerate.length}个文档`);
  }

  // 检查是否有已生成的文件需要覆盖
  const existingFiles = validFilesToGenerate.filter(fileType => isDocumentGenerated(fileType));

  if (existingFiles.length > 0) {
    const existingFileNames = existingFiles.map(fileType => {
      const docType = documentTypes.value.find(doc => doc.value === fileType);
      return docType?.label || fileType;
    }).join('、');

    try {
      await ElMessageBox.confirm(
        `以下文书已存在：${existingFileNames}。是否要覆盖现有文件？`,
        '确认覆盖',
        {
          confirmButtonText: '覆盖',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );
    } catch {
      return; // 用户取消操作
    }
  }

  generating.value = true;
  currentStep.value = 1;

  if (generationMode.value === 'batch') {
    batchFiles.value.forEach(file => {
      file.status = 'generating';
    });
  }

  try {
    // 调用后台接口，使用过滤后的有效文档列表
    const response = await generateDocument(caseId, {
      documentType: generationMode.value === 'single' ? validFilesToGenerate[0] : validFilesToGenerate.join(","),
      mode: generationMode.value
    });

    const result = response.data;
    
    // 模拟进度步骤
    const steps = [1000, 2000, 1500, 1000];
    let totalTime = 0;

    steps.forEach((time, index) => {
      totalTime += time;
      setTimeout(() => {
        currentStep.value = index + 2;
        if (index === steps.length - 1) {
          generating.value = false;
          hasGenerated.value = true; // 设置已生成状态
          if (generationMode.value === 'batch') {
            batchFiles.value.forEach(file => {
              file.status = 'success';
            });
          }
          
          // 显示成功消息和文件信息
          if (result.success) {
            ElMessage.success({
              message: generationMode.value === 'single'
                ? `文书生成完成！文件：${result.data?.fileName || '未知文件'}`
                : `批量生成完成！共生成${validFilesToGenerate.length}个文件`,
              duration: 3000
            });

            // 更新已生成文件列表，使用有效的文档列表
            const newGeneratedFiles = generationMode.value === 'single'
              ? validFilesToGenerate
              : validFilesToGenerate;

            newGeneratedFiles.forEach(fileType => {
              if (!generatedFiles.value.includes(fileType)) {
                generatedFiles.value.push(fileType);
              }
            });

            // 如果有文件URL，可以提供下载链接
            if (result.data?.fileUrl) {
              console.log('生成的文件URL:', result.data.fileUrl);
              // 可以在这里添加下载逻辑或显示文件链接
            }
          } else {
            ElMessage.error(result.msg || '文档生成失败');
            hasGenerated.value = false; // 生成失败时重置状态
          }
        }
      }, totalTime);
    });

  } catch (error) {
    generating.value = false;
    currentStep.value = 1;
    
    if (generationMode.value === 'batch') {
      batchFiles.value.forEach(file => {
        file.status = 'error';
        file.error = '生成失败';
      });
    }
    
    console.error('文档生成失败:', error);
    ElMessage.error('文档生成失败，请稍后重试');
  }
};

const navigateToFileDetail = (file) => {
  if (file.status === 'success') {
    router.push({
      name: '案卷文书详情',
      params: {
        fileId: file.id || Date.now()
      },
      query: {
        fileName: file.name,
        fileType: file.type,
        caseId: caseInfo.id || 'default'
      }
    });
  } else {
    ElMessage.warning('文件尚未生成完成');
  }
};

// 获取案件数据
const caseData = ref([]);
const getCaseData = async (name) => {
  try {
    const res = await getList(1, 20, { caseName: name });
    caseData.value = res.data.data.records.map(item => ({
      ...item,
    }));
  } catch (error) {
    console.error('获取案件数据失败:', error);
  }
};

// 获取案件已生成的文件列表
const fetchGeneratedFiles = async (caseId) => {
  if (!caseId) return;

  try {
    const param = {
      caseUuid: caseId
    }
    const res = await getDocumentList(1, 20, param);
    console.log(res.data.data.records);
    generatedFiles.value = res.data.data.records.map(item => item.documentType) || [];
    console.log('已生成的文件:', generatedFiles.value);
  } catch (error) {
    console.error('获取已生成文件失败:', error);
    generatedFiles.value = [];
  }
};

// 检查文档类型是否已生成
const isDocumentGenerated = (docValue) => {
  return generatedFiles.value.includes(docValue);
};

// 检查文档是否正在开发中
const isDocumentDeveloping = (docValue) => {
  const docType = documentTypes.value.find(doc => doc.value === docValue);
  return docType?.status === 'developing';
};

// 检查批量文件是否正在开发中
const isBatchFileDeveloping = (fileValue) => {
  const file = batchFiles.value.find(f => f.value === fileValue);
  return file?.developStatus === 'developing';
};

// 搜索过滤方法
const filterDocumentTypes = () => {
  if (!searchKeyword.value.trim()) {
    filteredDocumentTypes.value = documentTypes.value;
  } else {
    filteredDocumentTypes.value = documentTypes.value.filter(doc =>
      doc.label.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      doc.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  }
};

// 全选文件
const selectAllFiles = () => {
  selectedBatchFiles.value = batchFiles.value.map(file => file.value);
};

// 全不选
const unselectAllFiles = () => {
  selectedBatchFiles.value = [];
};

// 重置为默认选择
const resetDefaultSelection = () => {
  selectedBatchFiles.value = batchFiles.value
    .filter(file => file.checked && file.developStatus === 'completed')
    .map(file => file.value);
};

// 切换文件选择状态
const toggleFileSelection = (fileId) => {
  const index = selectedBatchFiles.value.indexOf(fileId);
  if (index > -1) {
    selectedBatchFiles.value.splice(index, 1);
  } else {
    selectedBatchFiles.value.push(fileId);
  }
};

// 处理文档类型点击
const handleDocTypeClick = (docValue) => {
  if (!caseInfo.name || !caseInfo.id) {
    ElMessage.warning('请先选择案件');
    return;
  }

  // 检查文档是否正在开发中
  const docType = documentTypes.value.find(doc => doc.value === docValue);
  if (docType?.status === 'developing') {
    ElMessage.warning('该文档类型正在开发中，敬请期待');
    return;
  }

  selectedDocType.value = docValue;
};

// 处理批量文件点击
const handleBatchFileClick = (fileValue) => {
  if (!caseInfo.name || !caseInfo.id) {
    ElMessage.warning('请先选择案件');
    return;
  }

  // 检查文件是否正在开发中
  const file = batchFiles.value.find(f => f.value === fileValue);
  if (file?.developStatus === 'developing') {
    ElMessage.warning('该文档类型正在开发中，敬请期待');
    return;
  }

  toggleFileSelection(fileValue);
};

// 处理生成模式切换
const onGenerationModeChange = (mode) => {
  if (mode === 'batch' && !hasAvailableDocuments.value) {
    ElMessage.warning('批量模式暂无可用文档，请选择单独生成模式');
    // 如果没有可用文档，切换回单独生成模式
    generationMode.value = 'single';
    return;
  }

  // 切换到批量模式时，重置选择为默认的已完成文档
  if (mode === 'batch') {
    resetDefaultSelection();
  }
};

// 检查是否有可用的已完成开发的文档
const hasAvailableDocuments = computed(() => {
  return batchFiles.value.some(file => file.developStatus === 'completed');
});

// 修改生成按钮的禁用状态
const isGenerateDisabled = computed(() => {
  if (!caseInfo.name || !caseInfo.id || generating.value) {
    return true;
  }

  // 单独生成模式：检查选中的文档是否正在开发中
  if (generationMode.value === 'single') {
    if (!selectedDocType.value) return true;
    const docType = documentTypes.value.find(doc => doc.value === selectedDocType.value);
    return docType?.status === 'developing';
  }

  // 批量生成模式：检查是否有选中的文件且都不是开发中状态
  if (generationMode.value === 'batch') {
    if (selectedBatchFiles.value.length === 0) return true;
    // 检查选中的文件中是否有开发中的文档
    const hasCompletedFiles = selectedBatchFiles.value.some(fileValue => {
      const file = batchFiles.value.find(f => f.value === fileValue);
      return file?.developStatus === 'completed';
    });
    return !hasCompletedFiles;
  }

  return false;
});

// 初始化时设置默认选择
onMounted(async () => {
  filteredDocumentTypes.value = documentTypes.value;
  await getCaseData();
  resetDefaultSelection(); // 设置默认勾选项
  
  // 检查是否需要自动选择案件
  if (route.query.autoSelect === 'true' && route.query.caseId) {
    await autoSelectCase(route.query.caseId, route.query.caseName);
  }
});

// 自动选择案件的方法
const autoSelectCase = async (caseId, caseName) => {
  try {
    // 如果有案件名称，直接设置
    if (caseName) {
      caseInfo.name = caseName;
      await onCaseNameChange(caseName);
    } else {
      // 如果只有ID，需要查找对应的案件
      const foundCase = caseData.value.find(item => 
        item.id === caseId || item.caseId === caseId
      );
      
      if (foundCase) {
        caseInfo.name = foundCase.caseName;
        await onCaseNameChange(foundCase.caseName);
      }
    }
    
    ElMessage.success('已自动选择案件');
  } catch (error) {
    console.error('自动选择案件失败:', error);
    ElMessage.warning('自动选择案件失败，请手动选择');
  }
};
</script>

<style scoped>
@import '@/styles/case-common.scss';


/* 右侧面板样式增强 */
.right-panel .section-card {
  border-left: 1px solid #409eff;
}

/* 进度容器 - 减少padding */
.progress-container {
  padding: 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.progress-container:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.progress-container :deep(.el-step__title) {
  font-size: 13px;
  font-weight: 500;
}

.progress-container :deep(.el-step__description) {
  font-size: 11px;
}

.progress-container :deep(.el-step__icon) {
  border-color: #409eff;
}

.progress-container :deep(.el-step__icon.is-success) {
  background-color: #67c23a;
  border-color: #67c23a;
}

.progress-container :deep(.el-step__main) {
  margin-left: 28px;
}

/* 提示区域 - 减少padding */
.tips-section-merged {
  margin-top: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #fffbf0 0%, #fef7e0 100%);
  border: 1px solid #ffd666;
  border-radius: 6px;
}

.tips-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #e6a23c;
  margin-bottom: 12px;
}

.tips-list {
  margin: 0;
  padding: 0;
  color: #666;
}

.tips-list li {
  font-size: 12px;
  line-height: 1.5;
  margin-bottom: 4px;
  position: relative;
  padding-left: 12px;
}

.tips-list li::before {
  content: '•';
  color: #e6a23c;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 右侧面板布局优化 */
.right-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: sticky;
  height: fit-content;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

/* 开发中文档样式 */
.doc-list-item.developing {
  opacity: 0.6;
  cursor: not-allowed !important;
  background-color: #f5f5f5 !important;
}

.doc-list-item.developing:hover {
  box-shadow: none !important;
  transform: none !important;
}

.doc-list-item.developing .doc-list-content {
  pointer-events: none;
}

.developing-tag {
  background-color: #fdf6ec !important;
  color: #e6a23c !important;
  border-color: #f5dab1 !important;
}

.developing-tag-batch {
  background-color: #fdf6ec !important;
  color: #e6a23c !important;
  border-color: #f5dab1 !important;
  margin-left: 8px;
}

/* 批量文件开发中样式 */
.doc-type-item.developing {
  opacity: 0.6;
  cursor: not-allowed !important;
  background-color: #f5f5f5 !important;
}

.doc-type-item.developing:hover {
  background-color: #f5f5f5 !important;
  transform: none !important;
}

.doc-type-item.developing .doc-type-content {
  pointer-events: none;
}



</style>
